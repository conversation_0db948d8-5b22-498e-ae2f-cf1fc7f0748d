import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';
import 'widgets/channel_picker.dart';
import '../../core/services/database_service.dart';
import 'package:sqflite/sqflite.dart';
import '../../l10n/app_localizations.dart';
import '../../core/protocol/tk8620_request_sender.dart';
import '../../core/bluetooth/bluetooth_manager.dart';
import '../../core/device/device_manager.dart';
import '../../core/protocol/di_data_dispatcher.dart' hide DiDataDispatcher;
// 上行保留路径但隐藏名，避免冲突
import 'listeners/chat_message_listener.dart';
// 不再直接解析 frame_decoder
import '../../core/protocol/tk8620_protocol.dart';
import 'dart:async';
import '../../core/services/channel_manager.dart';
import '../../core/services/conversation_manager.dart';
import '../../core/utils/group_util.dart';
import '../../core/constants/frequencies.dart';
import '../../core/protocol/at_commands.dart';
import '../../core/bluetooth/passthrough_gatt_helper.dart';
import 'widgets/message_bubble.dart';
import 'public_group_detail_screen.dart';
import '../../core/audio/ptt_recorder.dart';
import 'widgets/chat_action_panel.dart';
import 'ptt_talk_screen.dart';
import '../../core/services/conversation_display_service.dart';

/// 公共群会话界面，用于展示群聊消息并发送新消息。
class PublicGroupChatScreen extends StatefulWidget {
  final String groupName;
  final String conversationId;

  const PublicGroupChatScreen({
    super.key,
    required this.groupName,
    required this.conversationId,
  });

  @override
  State<PublicGroupChatScreen> createState() => _PublicGroupChatScreenState();
}

class _PublicGroupChatScreenState extends State<PublicGroupChatScreen> {
  final List<ChatMessage> _messages = [];
  // 列表滚动控制器
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _textController = TextEditingController();

  int _selectedChannel = 1;
  late String _groupName;
  late String _conversationId;
  StreamSubscription? _msgSub;
  // 监听会话显示名变化
  VoidCallback? _nameListener;
  // 语音状态监听订阅
  StreamSubscription<bool>? _recordingStateSub;
  // 是否处于语音发送模式
  bool _voiceMode = false;
  // 是否正在录音中
  bool _isRecording = false;
  // 是否显示底部功能面板
  bool _showActionPanel = false;

  @override
  void initState() {
    super.initState();
    _groupName = ConversationDisplayService.instance.getDisplayName(
      widget.conversationId,
      widget.groupName,
    );

    // 监听全局会话显示名变化
    _nameListener = () {
      final newName = ConversationDisplayService.instance.getDisplayName(
        _conversationId,
        _groupName,
      );
      if (newName != _groupName && mounted) {
        setState(() {
          _groupName = newName;
        });
      }
    };
    ConversationDisplayService.instance.namesNotifier.addListener(
      _nameListener!,
    );
    _conversationId = widget.conversationId;
    // 从 conversationId 推断信道初值
    final ch = GroupUtil.channelFromGroupId(_conversationId);
    if (ch != null) {
      _selectedChannel = ch;
    }

    // 设置全局当前信道
    ChannelManager.setChannel(_selectedChannel);

    // 发送频点切换指令
    _sendFreqSwitch(_selectedChannel);

    // 标记进入会话，并清零未读数
    ConversationManager.enter(_conversationId);
    _clearUnreadCount();

    // 加载历史消息
    _loadMessagesFromDb();

    _msgSub = ChatMessageListener.listen(_conversationId, (msg) {
      setState(() {
        _messages.add(msg);
        _scrollToBottom();
      });
    });

    // 监听数据库群组变更（如删除聊天记录）
    DatabaseService.groupChangedNotifier.addListener(_onGroupChanged);

    // 初始化语音处理器
    // _initVoiceProcessor(); // This line is removed as per the new_code
  }

  // 滚动到底部
  void _scrollToBottom() {
    if (!_scrollController.hasClients) return;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    });
  }

  // 初始化语音处理器
  // Future<void> _initVoiceProcessor() async {
  //   _voiceProcessor = await VoiceMessageProcessor.create();
  // }

  void _onGroupChanged() {
    // 仅当当前群组被操作时刷新，可直接重新加载消息
    _loadMessagesFromDb();
  }

  /// 从数据库加载指定群组的历史消息
  Future<void> _loadMessagesFromDb() async {
    final db = await DatabaseService.instance.database;
    final rows = await db.query(
      'group_messages',
      where: 'group_id = ? AND message_type IN (?, ?)',
      whereArgs: [_conversationId, TK8620DataType.text, TK8620DataType.voice],
      orderBy: 'created_at ASC',
    );

    final List<ChatMessage> history = rows.map((row) {
      final createdAt = DateTime.fromMillisecondsSinceEpoch(
        row['created_at'] as int,
      );
      final int type = row['message_type'] as int;
      if (type == TK8620DataType.voice) {
        return ChatMessage(
          srcId: row['src_id'] as int,
          content: '',
          isMine: (row['is_mine'] as int) == 1,
          time: _formatTime(createdAt),
          isVoice: true,
          voicePath: row['content'] as String,
        );
      } else {
        return ChatMessage(
          srcId: row['src_id'] as int,
          content: row['content'] as String,
          isMine: (row['is_mine'] as int) == 1,
          time: _formatTime(createdAt),
        );
      }
    }).toList();

    setState(() {
      _messages
        ..clear()
        ..addAll(history);
      _scrollToBottom();
    });
  }

  @override
  void dispose() {
    // 离开会话，恢复未读计数逻辑正常工作
    ConversationManager.exit();

    _msgSub?.cancel();
    _recordingStateSub?.cancel();
    _textController.dispose();
    // _voiceProcessor?.dispose(); // This line is removed as per the new_code

    // 移除监听
    DatabaseService.groupChangedNotifier.removeListener(_onGroupChanged);
    // 移除名称监听
    if (_nameListener != null) {
      ConversationDisplayService.instance.namesNotifier.removeListener(
        _nameListener!,
      );
    }
    super.dispose();
  }

  Future<void> _clearUnreadCount() async {
    final db = await DatabaseService.instance.database;
    await db.update(
      'group_conversations',
      {'unread_count': 0},
      where: 'conversation_id = ?',
      whereArgs: [_conversationId],
    );
    DatabaseService.groupChangedNotifier.value++;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.bgPrimary,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: context.bgPrimary,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          color: context.brandPrimary,
          onPressed: () => Navigator.pop(context),
        ),
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _groupName,
              style: TextStyle(
                color: context.textPrimaryCol,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            InkWell(
              onTap: _showChannelPicker,
              child: Icon(Icons.arrow_drop_down, color: context.textPrimaryCol),
            ),
          ],
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Image.asset(
              'assets/images/more_icon.png',
              width: 24,
              height: 24,
              color: context.textPrimaryCol,
            ),
            onPressed: () async {
              final newCh = await Navigator.push<int>(
                context,
                MaterialPageRoute(
                  builder: (_) => PublicGroupDetailScreen(
                    conversationId: _conversationId,
                    groupName: _groupName,
                    initialChannel: _selectedChannel,
                  ),
                ),
              );
              if (newCh != null && newCh != _selectedChannel) {
                await _switchToChannel(newCh);
              }
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final msg = _messages[index];
                return MessageBubble(message: msg);
              },
            ),
          ),
          Divider(height: 1, color: context.bgSecondary.withOpacity(0.5)),
          _buildInputBar(),
          // 底部加号展开的功能面板
          ChatActionPanel(
            visible: _showActionPanel,
            onPttPressed: () {
              setState(() {
                _showActionPanel = false;
              });
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => PttTalkScreen(
                    conversationId: _conversationId,
                    groupName: _groupName,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInputBar() {
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          children: [
            IconButton(
              icon: Icon(Icons.mic, color: context.brandPrimary),
              onPressed: () {
                setState(() {
                  _voiceMode = !_voiceMode;
                });
              },
            ),
            Expanded(
              child: _voiceMode
                  ? GestureDetector(
                      onLongPressStart: (_) {
                        _startRecording();
                      },
                      onLongPressEnd: (_) {
                        _stopRecording();
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 40,
                        decoration: BoxDecoration(
                          color: context.bgSecondary,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          _isRecording
                              ? AppLocalizations.of(
                                  context,
                                )!.publicChat_recording
                              : AppLocalizations.of(
                                  context,
                                )!.publicChat_pressHold,
                          style: TextStyle(color: context.textPrimaryCol),
                        ),
                      ),
                    )
                  : Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        color: context.bgSecondary,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: TextField(
                        controller: _textController,
                        textInputAction: TextInputAction.send,
                        onSubmitted: (_) => _handleSend(),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: AppLocalizations.of(
                            context,
                          )!.publicChat_inputHint,
                        ),
                      ),
                    ),
            ),
            const SizedBox(width: 8),
            IconButton(
              icon: Icon(
                Icons.add_circle_outline,
                color: context.brandPrimary,
                size: 28,
              ),
              onPressed: () {
                setState(() {
                  _showActionPanel = !_showActionPanel;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showChannelPicker() async {
    await showChannelPicker(
      context: context,
      initialChannel: _selectedChannel,
      onSelected: (ch) async {
        await _switchToChannel(ch);
      },
    );
  }

  Future<void> _switchToChannel(int ch) async {
    final newGroupId = GroupUtil.publicGroupId(ch);
    final newName = '公共群 - $ch';
    final db = await DatabaseService.instance.database;
    final nowSec = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    await db.insert('group_conversations', {
      'conversation_id': newGroupId,
      'group_id': newGroupId,
      'unread_count': 0,
      'last_msg_time': nowSec,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
    // 通知群组变更（公共群切换）
    DatabaseService.groupChangedNotifier.value++;
    setState(() {
      _selectedChannel = ch;
      _groupName = newName;
      _conversationId = newGroupId;
    });

    // 更新全局名称映射（列表页会再次计算，但这里先写一份，提升体验）
    ConversationDisplayService.instance.setDisplayName(newGroupId, newName);

    // 更新全局信道
    ChannelManager.setChannel(ch);

    // 发送频点切换指令
    _sendFreqSwitch(ch);

    // 重新加载该频道历史消息
    await _loadMessagesFromDb();

    // 更新当前会话 ID 以刷新首页高亮
    ConversationManager.enter(newGroupId);

    // 清零新群组未读数
    await _clearUnreadCount();
  }

  // 发送当前输入框中的文本消息
  void _handleSend() async {
    final text = _textController.text.trim();
    if (text.isEmpty) return;

    // 1. 先清空输入框
    _textController.clear();

    // 解析 SrcID（取DeviceID低8位，默认为1）
    int srcId = 0x01;
    final deviceIdHex = DeviceManager.instance.deviceIdNotifier.value;
    if (deviceIdHex != null && deviceIdHex.startsWith('0x')) {
      try {
        srcId = int.parse(deviceIdHex.substring(2), radix: 16) & 0xFF;
      } catch (_) {}
    }

    // 2. 本地添加到消息列表，刷新 UI
    final msg = ChatMessage(
      srcId: srcId,
      content: text,
      isMine: true,
      time: _formatTime(DateTime.now()),
    );
    setState(() {
      _messages.add(msg);
    });

    // 3. 获取已连接设备
    final device = BluetoothManager.currentDevice.value;
    if (device == null) {
      debugPrint('⚠️ 未连接设备，无法发送文本消息');
      return;
    }

    // 目标 ID 取当前群组十六进制 ID
    int dstId;
    try {
      final idStr =
          _conversationId.startsWith('0x') || _conversationId.startsWith('0X')
          ? _conversationId.substring(2)
          : _conversationId;
      dstId = int.parse(idStr, radix: 16);
    } catch (_) {
      debugPrint('❌ 解析群组 ID 失败，使用广播');
      dstId = 0xFFFFFFFF;
    }

    try {
      await TK8620RequestSender.sendTextMessage(
        device,
        text: text,
        srcId: srcId,
        dstId: dstId,
      );
    } catch (e) {
      debugPrint('❌ 发送文本消息失败: $e');
    }

    // 保存到数据库
    _saveMessageToDb(
      content: text,
      isMine: true,
      srcId: srcId,
      messageType: TK8620DataType.text,
    );
  }

  // 保存消息到 group_messages 表，并更新会话 last_msg_time
  Future<void> _saveMessageToDb({
    required String content,
    required bool isMine,
    required int srcId,
    required int messageType,
  }) async {
    final db = await DatabaseService.instance.database;
    final nowMs = DateTime.now().millisecondsSinceEpoch;

    await db.insert('group_messages', {
      'group_id': _conversationId,
      'src_id': srcId,
      'message_type': messageType,
      'content': content,
      'is_mine': isMine ? 1 : 0,
      'created_at': nowMs,
    });

    // 更新 group_conversations 的最后消息时间
    await db.update(
      'group_conversations',
      {'last_msg_time': nowMs ~/ 1000},
      where: 'conversation_id = ?',
      whereArgs: [_conversationId],
    );

    // 通知聊天列表刷新（活跃人数、未读数等）
    DatabaseService.groupChangedNotifier.value++;
  }

  // 格式化时间显示 HH:mm
  String _formatTime(DateTime dt) {
    final h = dt.hour.toString().padLeft(2, '0');
    final m = dt.minute.toString().padLeft(2, '0');
    return '$h:$m';
  }

  /// 根据 [channel] 发送频点切换 AT 指令。
  /// 若当前未连接设备，则直接返回。
  Future<void> _sendFreqSwitch(int channel) async {
    final device = BluetoothManager.currentDevice.value;
    if (device == null) {
      debugPrint('⚠️ 无连接设备，无法切换频点');
      return;
    }

    try {
      final int freq = Frequencies.publicChannelFreq(channel);
      final cmdBytes = getAtCommandBytes(
        AtCommandType.setFreq,
        params: {
          'txDataFreq': freq,
          'rxDataFreq': freq,
          'txBcnFreq': freq,
          'rxBcnFreq': freq,
        },
      );
      await PassthroughGattHelper.sendAtCommand(
        device,
        cmdBytes,
        withoutResponse: true,
      );
      debugPrint('✅ 已发送切换频点 AT 指令: channel=$channel, freq=$freq');
    } catch (e) {
      debugPrint('❌ 发送切换频点 AT 指令失败: $e');
    }
  }

  // 开始录音
  void _startRecording() async {
    if (_isRecording) return;
    await PttRecorder.instance.start();
    setState(() {
      _isRecording = true;
    });
  }

  // 停止录音并处理语音消息
  void _stopRecording() async {
    if (!_isRecording) return;
    setState(() {
      _isRecording = false;
    });
    await PttRecorder.instance.stop();
    debugPrint('✅ 实时录音结束');
  }
}
