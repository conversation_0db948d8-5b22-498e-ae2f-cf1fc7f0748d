import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';
import '../../core/services/database_service.dart';
import '../../l10n/app_localizations.dart';
import '../../core/audio/ptt_recorder.dart';
import '../../core/services/call_history_storage.dart';

/// PTT 对讲页面，用于实时语音对讲。
/// 顶部展示群成员列表，主体区域展示一个麦克风按键。
class PttTalkScreen extends StatefulWidget {
  final String conversationId;
  final String groupName;

  const PttTalkScreen({
    super.key,
    required this.conversationId,
    required this.groupName,
  });

  @override
  State<PttTalkScreen> createState() => _PttTalkScreenState();
}

class _PttTalkScreenState extends State<PttTalkScreen>
    with SingleTickerProviderStateMixin {
  // 成员列表
  List<_MemberInfo> _members = [];

  bool _isRecording = false;
  late final AnimationController _ringController;
  late final Animation<double> _ringAnim;

  @override
  void initState() {
    super.initState();
    _loadMembers();

    _ringController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _ringAnim = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _ringController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _ringController.dispose();
    super.dispose();
  }

  Future<void> _loadMembers() async {
    final db = await DatabaseService.instance.database;
    // 查询群成员及其昵称、头像索引
    final rows = await db.rawQuery(
      '''
      SELECT gm.device_id AS did,
             coalesce(gm.nickname, c.nickname) AS nickname,
             coalesce(gm.avatar_index, c.avatar_index, 0) AS avatar_index
      FROM group_members gm
      LEFT JOIN contacts c ON c.device_id = gm.device_id
      WHERE gm.group_id = ?
      ORDER BY gm.joined_at ASC
      LIMIT 50
    ''',
      [widget.conversationId],
    );

    setState(() {
      _members = rows
          .map(
            (r) => _MemberInfo(
              deviceId: r['did'] as String,
              nickname: (r['nickname'] as String?) ?? '未知',
              avatarIndex: (r['avatar_index'] as int?) ?? 0,
            ),
          )
          .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.groupName), centerTitle: true),
      body: Column(
        children: [
          _buildMemberBar(context),
          Expanded(
            child: Container(
              width: double.infinity,
              color: context.bgSecondary,
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Waveform placeholder
                    if (_isRecording) _Waveform(animation: _ringController),
                    const SizedBox(height: 20),
                    // Mic with ring (above code replaced here)
                    GestureDetector(
                      onLongPressStart: (_) async {
                        setState(() {
                          _isRecording = true;
                        });
                        _ringController.repeat(reverse: true);
                        await PttRecorder.instance.start();
                      },
                      onLongPressEnd: (_) async {
                        setState(() {
                          _isRecording = false;
                        });
                        _ringController
                          ..stop()
                          ..reset();
                        await PttRecorder.instance.stop();

                        // 记录本会话已通话，供通话记录列表显示“已接通”
                        await CallHistoryStorage.instance.markPttSent(
                          widget.conversationId,
                        );
                      },
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Outer pulsating ring
                          if (_isRecording)
                            AnimatedBuilder(
                              animation: _ringAnim,
                              builder: (context, child) {
                                final double scale =
                                    1.0 + 0.3 * _ringAnim.value;
                                return Container(
                                  width: 160 * scale,
                                  height: 160 * scale,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.red.withOpacity(
                                      0.3 * (1 - _ringAnim.value),
                                    ),
                                  ),
                                );
                              },
                            ),
                          // Mic button
                          Container(
                            width: 140,
                            height: 140,
                            decoration: BoxDecoration(
                              color: _isRecording
                                  ? Colors.redAccent
                                  : context.brandPrimary,
                              shape: BoxShape.circle,
                            ),
                            alignment: Alignment.center,
                            child: const Icon(
                              Icons.mic,
                              color: Colors.white,
                              size: 60,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemberBar(BuildContext context) {
    if (_members.isEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        alignment: Alignment.center,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.group_off, size: 64, color: context.textSecondaryCol),
            const SizedBox(height: 8),
            Text(
              AppLocalizations.of(context)!.ptt_noMembers,
              style: TextStyle(fontSize: 14, color: context.textSecondaryCol),
            ),
          ],
        ),
      );
    }
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            const SizedBox(width: 12),
            ..._members.map((m) => _buildMemberItem(context, m)),
            const SizedBox(width: 12),
          ],
        ),
      ),
    );
  }

  Widget _buildMemberItem(BuildContext context, _MemberInfo m) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildAvatar(context, m),
          const SizedBox(height: 4),
          SizedBox(
            width: 50,
            child: Text(
              m.displayName,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12, color: context.textPrimaryCol),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context, _MemberInfo m) {
    final palette = [
      Colors.indigo,
      Colors.green,
      Colors.deepOrange,
      Colors.teal,
      Colors.purple,
      Colors.brown,
      Colors.blueGrey,
      Colors.pink,
      Colors.cyan,
      Colors.amber,
    ];
    final idx = m.deviceId.hashCode % palette.length;
    final bg = palette[idx];
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: bg,
        borderRadius: BorderRadius.circular(24),
      ),
      alignment: Alignment.center,
      child: Text(
        m.initial,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

class _MemberInfo {
  final String deviceId;
  final String nickname;
  final int avatarIndex;

  _MemberInfo({
    required this.deviceId,
    required this.nickname,
    required this.avatarIndex,
  });

  String get displayName => nickname.isNotEmpty ? nickname : deviceId;
  String get initial => displayName.substring(0, 1);
}

// A simple animated waveform using bars whose height animates with controller.
class _Waveform extends StatelessWidget {
  final Animation<double> animation;
  const _Waveform({required this.animation});

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final bars = List.generate(20, (index) {
          final rnd = (index * 37) % 100 / 100; // pseudo random
          final height = 10 + 40 * (rnd * (0.5 + 0.5 * animation.value));
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2),
            child: Container(
              width: 4,
              height: height,
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        });
        return Row(mainAxisSize: MainAxisSize.min, children: bars);
      },
    );
  }
}
