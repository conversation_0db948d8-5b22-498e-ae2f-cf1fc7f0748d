import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../core/constants/colors.dart';
import '../../core/services/settings_service.dart';

/// 通话音频设置页面
class CallAudioSettingsScreen extends StatefulWidget {
  const CallAudioSettingsScreen({super.key});

  @override
  State<CallAudioSettingsScreen> createState() => _CallAudioSettingsScreenState();
}

class _CallAudioSettingsScreenState extends State<CallAudioSettingsScreen> {
  bool _useEarpieceMode = true; // 默认使用听筒模式
  bool _enableEchoCancellation = true; // 启用回音消除
  bool _enableNoiseSuppression = true; // 启用噪音抑制

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// 加载设置
  void _loadSettings() async {
    // 从持久化存储加载设置
    final useEarpiece = await SettingsService.getCallUseEarpieceMode();
    final enableEcho = await SettingsService.getCallEnableEchoCancellation();
    final enableNoise = await SettingsService.getCallEnableNoiseSuppression();
    
    setState(() {
      _useEarpieceMode = useEarpiece ?? true;
      _enableEchoCancellation = enableEcho ?? true;
      _enableNoiseSuppression = enableNoise ?? true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        backgroundColor: context.tileBackground,
        elevation: 0,
        iconTheme: IconThemeData(color: context.textPrimaryCol),
        title: Text(
          loc.settings_callAudio,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: ListView(
        children: [
          const SizedBox(height: 12),
          
          // 听筒模式设置
          _buildSwitchTile(
            title: loc.callAudio_earpieceMode,
            description: loc.callAudio_earpieceMode_desc,
            value: _useEarpieceMode,
            onChanged: (value) {
              setState(() => _useEarpieceMode = value);
              SettingsService.saveCallUseEarpieceMode(value);
            },
          ),
          
          const SizedBox(height: 8),
          
          // 回音消除设置
          _buildSwitchTile(
            title: loc.callAudio_echoCancellation,
            description: loc.callAudio_echoCancellation_desc,
            value: _enableEchoCancellation,
            onChanged: (value) {
              setState(() => _enableEchoCancellation = value);
              SettingsService.saveCallEnableEchoCancellation(value);
            },
          ),
          
          const SizedBox(height: 8),
          
          // 噪音抑制设置
          _buildSwitchTile(
            title: loc.callAudio_noiseSuppression,
            description: loc.callAudio_noiseSuppression_desc,
            value: _enableNoiseSuppression,
            onChanged: (value) {
              setState(() => _enableNoiseSuppression = value);
              SettingsService.saveCallEnableNoiseSuppression(value);
            },
          ),
          
          const SizedBox(height: 24),
          
          // 说明信息
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: context.tileBackground,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  loc.callAudio_tips_title,
                  style: TextStyle(
                    color: context.textPrimaryCol,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  loc.callAudio_tips_content,
                  style: TextStyle(
                    color: context.textSecondaryCol,
                    fontSize: 12,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String description,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      color: context.tileBackground,
      child: SwitchListTile(
        title: Text(
          title,
          style: TextStyle(color: context.textPrimaryCol, fontSize: 16),
        ),
        subtitle: Text(
          description,
          style: TextStyle(color: context.textSecondaryCol, fontSize: 12),
        ),
        value: value,
        activeColor: context.brandPrimary,
        onChanged: onChanged,
      ),
    );
  }
}
