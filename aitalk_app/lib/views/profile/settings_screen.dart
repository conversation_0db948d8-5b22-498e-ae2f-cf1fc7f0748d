import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../core/constants/colors.dart';
import 'language_settings_screen.dart';
import 'theme_settings_screen.dart';
import 'voice_playback_settings_screen.dart';
import 'call_audio_settings_screen.dart';
import 'notification_settings_screen.dart';
import 'storage_management_screen.dart';
import 'text_size_settings_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        backgroundColor: context.bgPrimary,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: context.textPrimaryCol),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          loc.settings_title,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: ListView(
        children: [
          // 显示设置组
          _buildSectionHeader(context, loc.settings_displaySettings),
          _buildSettingItem(
            context: context,
            title: loc.settings_multilanguage,
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (_) => const LanguageSettingsScreen(),
                ),
              );
            },
          ),
          _buildSettingItem(
            context: context,
            title: loc.settings_themeMode,
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => const ThemeSettingsScreen()),
              );
            },
          ),
          _buildSettingItem(
            context: context,
            title: loc.settings_fontSize,
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (_) => const TextSizeSettingsScreen(),
                ),
              );
            },
          ),

          // 聊天设置组
          _buildSectionHeader(context, loc.settings_chatSettings),
          _buildSettingItem(
            context: context,
            title: loc.settings_voicePlayback,
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => const VoicePlaybackSettingsScreen(),
              ),
            ),
          ),
          _buildSettingItem(
            context: context,
            title: loc.settings_callAudio,
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => const CallAudioSettingsScreen(),
              ),
            ),
          ),
          _buildSettingItem(
            context: context,
            title: loc.settings_notifications,
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => const NotificationSettingsScreen(),
              ),
            ),
          ),

          // 存储设置组
          _buildSectionHeader(context, loc.settings_storageSettings),
          _buildSettingItem(
            context: context,
            title: loc.settings_storageManagement,
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (_) => const StorageManagementScreen(),
                ),
              );
            },
          ),

          // 其他组
          _buildSectionHeader(context, loc.settings_other),
          _buildSettingItem(
            context: context,
            title: loc.settings_helpAndFeedback,
            onTap: () {},
          ),

          // 关于aiTalk
          _buildSettingItem(
            context: context,
            title: loc.settings_about,
            trailing: Text(
              loc.settings_version('1.0.0'),
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            ),
            onTap: () {},
          ),

          // 底部填充
          const SizedBox(height: 40),
        ],
      ),
    );
  }

  // 分组标题
  Widget _buildSectionHeader(BuildContext context, String title) {
    return Container(
      padding: const EdgeInsets.only(left: 16, top: 12, bottom: 4),
      color: context.bgSecondary,
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.normal,
          color: context.textSecondaryCol,
        ),
      ),
    );
  }

  // 设置项
  Widget _buildSettingItem({
    required BuildContext context,
    required String title,
    Widget? trailing,
    required VoidCallback onTap,
  }) {
    return Container(
      color: context.tileBackground,
      child: ListTile(
        title: Text(
          title,
          style: TextStyle(color: context.textPrimaryCol, fontSize: 16),
        ),
        trailing:
            trailing ??
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: context.textSecondaryCol,
            ),
        onTap: onTap,
      ),
    );
  }
}
