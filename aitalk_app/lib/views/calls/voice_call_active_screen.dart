import 'dart:async';
import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';
import '../../core/services/database_service.dart';
import '../../l10n/app_localizations.dart';
import '../../core/audio/real_time_voice_processor.dart';
import '../../core/audio/call_audio_session_manager.dart';
import '../../core/services/call_history_storage.dart';
import '../../core/protocol/at_commands.dart';
import '../../core/protocol/work_mode_constants.dart';
import '../../core/bluetooth/bluetooth_manager.dart';
import '../../core/bluetooth/passthrough_gatt_helper.dart';

/// 实时通话界面，参考PTT界面但无PTT按钮，持续录音
class VoiceCallActiveScreen extends StatefulWidget {
  final String conversationId;
  final String groupName;
  final int sessionId;
  final int? callerSrcId; // 发起方的SrcId，接收方需要

  const VoiceCallActiveScreen({
    super.key,
    required this.conversationId,
    required this.groupName,
    required this.sessionId,
    this.callerSrcId,
  });

  @override
  State<VoiceCallActiveScreen> createState() => _VoiceCallActiveScreenState();
}

class _VoiceCallActiveScreenState extends State<VoiceCallActiveScreen>
    with SingleTickerProviderStateMixin {
  // 成员列表
  List<_MemberInfo> _members = [];

  bool _isCallActive = true;
  bool _isMuted = false;
  bool _isSpeakerEnabled = false; // 扬声器状态
  late final AnimationController _waveController;
  late final Animation<double> _waveAnim;

  // 通话时长计时器
  Timer? _durationTimer;
  int _callDurationSeconds = 0;

  @override
  void initState() {
    super.initState();
    _loadMembers();
    _startCall();

    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _waveAnim = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeInOut),
    );

    // 开始计时
    _startDurationTimer();
  }

  @override
  void dispose() {
    _endCall();
    _waveController.dispose();
    _durationTimer?.cancel();
    super.dispose();
  }

  /// 开始通话
  void _startCall() async {
    try {
      debugPrint('📞 开始实时通话录音');
      await RealTimeVoiceProcessor.instance.startRecording();
      _waveController.repeat(reverse: true);

      // 同步扬声器状态（默认为听筒模式）
      setState(() {
        _isSpeakerEnabled = CallAudioSessionManager.instance.isSpeakerEnabled;
      });

      // 记录通话开始
      await CallHistoryStorage.instance.markVoiceInviteReceived(
        widget.conversationId,
      );
    } catch (e) {
      debugPrint('❌ 开始通话失败: $e');
    }
  }

  /// 结束通话
  void _endCall() async {
    if (!_isCallActive) return;

    setState(() {
      _isCallActive = false;
    });

    try {
      debugPrint('📞 结束实时通话录音');
      await RealTimeVoiceProcessor.instance.dispose();
      _waveController.stop();
      _durationTimer?.cancel();

      // 切换回异步收发模式
      await _switchToAsyncMode();
    } catch (e) {
      debugPrint('❌ 结束通话失败: $e');
    }
  }

  /// 开始通话时长计时
  void _startDurationTimer() {
    _durationTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isCallActive) {
        timer.cancel();
        return;
      }
      setState(() {
        _callDurationSeconds++;
      });
    });
  }

  /// 格式化通话时长
  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  /// 切换静音状态
  void _toggleMute() async {
    setState(() {
      _isMuted = !_isMuted;
    });

    try {
      if (_isMuted) {
        // 静音：停止音频采集
        debugPrint('🔇 静音：停止音频采集');
        await RealTimeVoiceProcessor.instance.stopRecording();
        _waveController.stop();
      } else {
        // 取消静音：恢复音频采集
        debugPrint('🎤 取消静音：恢复音频采集');
        await RealTimeVoiceProcessor.instance.startRecording();
        _waveController.repeat(reverse: true);
      }
    } catch (e) {
      debugPrint('❌ 切换静音状态失败: $e');
      // 如果操作失败，恢复UI状态
      setState(() {
        _isMuted = !_isMuted;
      });
    }
  }

  /// 挂断通话
  void _hangUp() {
    _endCall();
    Navigator.of(context).pop();
  }

  /// 切换扬声器状态
  void _toggleSpeaker() async {
    try {
      final newSpeakerState = !_isSpeakerEnabled;
      debugPrint('🔊 切换扬声器状态: ${newSpeakerState ? "启用" : "禁用"}');

      final success = await CallAudioSessionManager.instance.setSpeakerEnabled(
        newSpeakerState,
      );

      if (success) {
        setState(() {
          _isSpeakerEnabled = newSpeakerState;
        });
        debugPrint('✅ 扬声器状态切换成功');
      } else {
        debugPrint('❌ 扬声器状态切换失败');
      }
    } catch (e) {
      debugPrint('❌ 切换扬声器状态异常: $e');
    }
  }

  /// 加载群组成员
  Future<void> _loadMembers() async {
    try {
      final db = await DatabaseService.instance.database;
      final result = await db.query(
        'group_members',
        where: 'group_id = ?',
        whereArgs: [widget.conversationId],
      );

      final members = <_MemberInfo>[];
      for (final row in result) {
        final deviceId = row['device_id'] as String;
        final nickname = row['nickname'] as String? ?? '匿名用户';
        members.add(
          _MemberInfo(
            deviceId: deviceId,
            nickname: nickname,
            initial: nickname.isNotEmpty ? nickname[0] : '?',
          ),
        );
      }

      setState(() {
        _members = members;
      });
    } catch (e) {
      debugPrint('❌ 加载群组成员失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.groupName),
        centerTitle: true,
        automaticallyImplyLeading: false, // 隐藏返回按钮
      ),
      body: Column(
        children: [
          _buildMemberBar(context),
          Expanded(
            child: Container(
              width: double.infinity,
              color: context.bgSecondary,
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 通话状态
                    Text(
                      _isCallActive
                          ? AppLocalizations.of(context)!.voiceCall_calling
                          : AppLocalizations.of(context)!.voiceCall_ended,
                      style: TextStyle(
                        fontSize: 18,
                        color: context.textSecondaryCol,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // 通话时长
                    Text(
                      _formatDuration(_callDurationSeconds),
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: context.textPrimaryCol,
                      ),
                    ),
                    const SizedBox(height: 40),

                    // 音频波形动画（只在未静音且通话活跃时显示）
                    if (_isCallActive && !_isMuted)
                      _Waveform(animation: _waveController)
                    else if (_isCallActive && _isMuted)
                      // 静音状态显示静音图标
                      Icon(
                        Icons.mic_off,
                        size: 64,
                        color: context.textSecondaryCol,
                      ),
                    const SizedBox(height: 40),

                    // 控制按钮
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // 静音按钮
                        _buildControlButton(
                          icon: _isMuted ? Icons.mic_off : Icons.mic,
                          color: _isMuted ? Colors.red : context.textPrimaryCol,
                          backgroundColor: _isMuted
                              ? context.bgPrimary
                              : context.brandPrimary.withValues(alpha: 0.3),
                          onPressed: _toggleMute,
                        ),

                        // 挂断按钮
                        _buildControlButton(
                          icon: Icons.call_end,
                          color: Colors.white,
                          backgroundColor: Colors.red,
                          onPressed: _hangUp,
                          size: 70,
                        ),

                        // 扬声器按钮
                        _buildControlButton(
                          icon: _isSpeakerEnabled
                              ? Icons.volume_up
                              : Icons.phone,
                          color: _isSpeakerEnabled
                              ? Colors.white
                              : context.textPrimaryCol,
                          backgroundColor: _isSpeakerEnabled
                              ? context.brandPrimary
                              : context.brandPrimary.withValues(alpha: 0.3),
                          onPressed: _toggleSpeaker,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required Color color,
    required Color backgroundColor,
    required VoidCallback onPressed,
    double size = 60,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: Icon(icon, color: color, size: size * 0.5),
      ),
    );
  }

  Widget _buildMemberBar(BuildContext context) {
    if (_members.isEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        alignment: Alignment.center,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.group_off, size: 64, color: context.textSecondaryCol),
            const SizedBox(height: 8),
            Text(
              AppLocalizations.of(context)!.ptt_noMembers,
              style: TextStyle(fontSize: 14, color: context.textSecondaryCol),
            ),
          ],
        ),
      );
    }
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            const SizedBox(width: 12),
            ..._members.map((m) => _buildMemberItem(context, m)),
            const SizedBox(width: 12),
          ],
        ),
      ),
    );
  }

  Widget _buildMemberItem(BuildContext context, _MemberInfo m) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildAvatar(context, m),
          const SizedBox(height: 8),
          Text(
            m.nickname,
            style: TextStyle(fontSize: 12, color: context.textSecondaryCol),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context, _MemberInfo m) {
    final palette = [
      Colors.indigo,
      Colors.green,
      Colors.deepOrange,
      Colors.teal,
      Colors.purple,
      Colors.brown,
      Colors.blueGrey,
      Colors.pink,
      Colors.cyan,
      Colors.amber,
    ];
    final idx = m.deviceId.hashCode % palette.length;
    final bg = palette[idx];
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: bg,
        borderRadius: BorderRadius.circular(24),
      ),
      alignment: Alignment.center,
      child: Text(
        m.initial,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 切换回异步收发模式和默认速率
  Future<void> _switchToAsyncMode() async {
    try {
      debugPrint('📡 切换回异步收发模式和默认速率');

      final device = BluetoothManager.currentDevice.value;
      if (device == null) {
        debugPrint('❌ 没有连接的设备，无法切换工作模式');
        return;
      }

      // 1. 恢复默认速率模式
      final rateBytes = getAtCommandBytes(
        AtCommandType.setRate,
        params: {'rateMode': RateMode.defaultRate}, // 默认速率模式
      );

      await PassthroughGattHelper.sendAtCommand(
        device,
        rateBytes,
        withoutResponse: true,
      );

      debugPrint(
        '✅ 速率模式恢复完成: ${RateMode.getDescription(RateMode.defaultRate)}',
      );

      // 等待速率配置生效
      await Future.delayed(const Duration(milliseconds: 300));

      // 2. 配置工作模式为异步收发模式 (21)
      final workModeBytes = getAtCommandBytes(
        AtCommandType.workMode,
        params: {'mode': RealTimeCallWorkMode.defaultMode}, // 异步收发工作模式
      );

      await PassthroughGattHelper.sendAtCommand(
        device,
        workModeBytes,
        withoutResponse: true,
      );

      debugPrint(
        '✅ 工作模式切换完成: ${WorkMode.getDescription(RealTimeCallWorkMode.defaultMode)}',
      );

      // 等待配置生效
      await Future.delayed(const Duration(milliseconds: 300));
    } catch (e) {
      debugPrint('❌ 切换回异步模式失败: $e');
    }
  }
}

/// 成员信息
class _MemberInfo {
  final String deviceId;
  final String nickname;
  final String initial;

  _MemberInfo({
    required this.deviceId,
    required this.nickname,
    required this.initial,
  });
}

/// 音频波形动画
class _Waveform extends StatelessWidget {
  final Animation<double> animation;
  const _Waveform({required this.animation});

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final bars = List.generate(20, (index) {
          final rnd = (index * 37) % 100 / 100; // pseudo random
          final height = 10 + 40 * (rnd * (0.5 + 0.5 * animation.value));
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2),
            child: Container(
              width: 4,
              height: height,
              decoration: BoxDecoration(
                color: Colors.redAccent,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        });
        return Row(mainAxisSize: MainAxisSize.min, children: bars);
      },
    );
  }
}
