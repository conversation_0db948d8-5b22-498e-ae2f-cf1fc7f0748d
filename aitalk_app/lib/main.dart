import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'dart:async' show unawaited;
import 'l10n/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'core/constants/colors.dart';
import 'core/bluetooth/bluetooth_manager.dart';
import 'core/bluetooth/auto_reconnector.dart';
import 'core/bluetooth/connection_keeper.dart';
import 'views/main_navigation_screen.dart';
import 'core/services/database_service.dart';
import 'core/protocol/di_data_dispatcher.dart'; // diData 分发器单例
import 'core/services/message_service.dart';
import 'core/services/notification_service.dart';
import 'core/services/settings_service.dart';
import 'core/services/active_group_storage.dart';
import 'core/services/conversation_manager.dart';

// 全局导航器键，用于在任意位置访问导航器上下文
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// 全局语言通知器，可在任意位置修改 value 以切换语言
// 默认语言设为中文，如需跟随系统可将其置为 null
final ValueNotifier<Locale?> localeNotifier = ValueNotifier<Locale?>(
  const Locale('zh'),
);
// 全局主题模式通知器
final ValueNotifier<ThemeMode> themeModeNotifier = ValueNotifier<ThemeMode>(
  ThemeMode.system,
);
// 全局文字缩放通知器（1.0 为系统默认）
final ValueNotifier<double> textScaleNotifier = ValueNotifier<double>(1.0);

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // Disable flutter_blue_plus internal debug logs
  FlutterBluePlus.setLogLevel(LogLevel.none);
  // 预初始化蓝牙插件，减少首次进入扫描页延迟
  final storedLocale = await SettingsService.loadLocale();
  if (storedLocale != null || localeNotifier.value != storedLocale) {
    localeNotifier.value = storedLocale;
  }
  await BluetoothManager.ensureInitialized();
  // 加载上次保存的 UI 设置 (主题/文字大小)
  final storedTheme = await SettingsService.loadThemeMode();
  if (storedTheme != null) themeModeNotifier.value = storedTheme;
  final storedScale = await SettingsService.loadTextScale();
  if (storedScale != null) textScaleNotifier.value = storedScale;

  // 启动后台 ConnectionKeeper，实时保持连接
  ConnectionKeeper.ensureInitialized();
  // 初始化本地数据库
  await DatabaseService.instance.database;
  // 加载持久化的激活群组信息，恢复高亮状态
  final activeGroup = await ActiveGroupStorage.load();
  if (activeGroup != null) {
    ConversationManager.lastConversationId.value = activeGroup.$1; // groupId
  }
  // 初始化 diData 分发器，建立 DI 数据监听（只需调用一次即可）
  DiDataDispatcher.instance;
  // 启动全局 MessageService，确保后台持久化消息
  MessageService.instance;
  // 初始化本地通知服务
  await NotificationService.instance.init();
  // 尝试自动重连上一台设备（后台进行，不阻塞启动速度）
  unawaited(AutoReconnector.tryReconnect());
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<double>(
      valueListenable: textScaleNotifier,
      builder: (context, textScale, _child) {
        return ValueListenableBuilder<Locale?>(
          valueListenable: localeNotifier,
          builder: (context, locale, _) {
            return ValueListenableBuilder<ThemeMode>(
              valueListenable: themeModeNotifier,
              builder: (context, themeMode, __) {
                return MaterialApp(
                  navigatorKey: navigatorKey,
                  title: 'aiTalk',
                  debugShowCheckedModeBanner: false,
                  locale: locale, // 若为 null 则跟随系统
                  localizationsDelegates: const [
                    AppLocalizations.delegate,
                    GlobalMaterialLocalizations.delegate,
                    GlobalCupertinoLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate,
                  ],
                  supportedLocales: const [Locale('en'), Locale('zh')],
                  theme: ThemeData(
                    colorScheme: ColorScheme.light(
                      primary: AppColors.primary,
                      secondary: AppColors.accent,
                    ),
                    scaffoldBackgroundColor: AppColors.backgroundPrimary,
                    appBarTheme: const AppBarTheme(
                      backgroundColor: AppColors.backgroundPrimary,
                      elevation: 0,
                      iconTheme: IconThemeData(color: AppColors.textPrimary),
                      titleTextStyle: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  darkTheme: ThemeData(
                    colorScheme: ColorScheme.dark(
                      primary: AppColors.primary,
                      secondary: AppColors.accent,
                    ),
                  ),
                  themeMode: themeMode,
                  builder: (context, child) {
                    // Apply global text scale factor
                    return MediaQuery(
                      data: MediaQuery.of(
                        context,
                      ).copyWith(textScaleFactor: textScale),
                      child: child ?? const SizedBox.shrink(),
                    );
                  },
                  home: const MainNavigationScreen(),
                );
              },
            );
          },
        );
      },
    );
  }
}
