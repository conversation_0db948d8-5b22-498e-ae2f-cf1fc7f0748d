import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import '../services/database_service.dart';
import '../services/conversation_manager.dart';
import '../services/call_history_storage.dart';
import '../protocol/tk8620_protocol.dart';
import 'package:sqflite/sqflite.dart';

/// 语音 PCM 缓存 & WAV 保存器。
///
/// 调用 [handlePcm] 逐帧追加同一 srcId 的 PCM 数据；当 [isLastPacket] 为 true
/// 时，会将累计的 PCM 合并为 WAV 文件并保存至应用文档目录，随后延迟 1 秒播放。
class VoiceWavSaver {
  VoiceWavSaver._();

  static final VoiceWavSaver instance = VoiceWavSaver._();

  final Map<int, BytesBuilder> _pcmCache = {};
  final AudioPlayer _player = AudioPlayer();

  /// 统一处理语音 PCM。
  ///
  /// * [srcId]     发送者短地址
  /// * [pcm]       解码后的 PCM 帧
  /// * [isLastPacket] 是否为语音数据最后一包
  /// * [isMine]    是否我方发送（影响 is_mine 字段）
  Future<String?> handleVoice({
    required int srcId,
    required Int16List pcm,
    required bool isLastPacket,
    required bool isMine,
  }) async {
    _pcmCache.putIfAbsent(srcId, () => BytesBuilder());
    _pcmCache[srcId]!.add(pcm.buffer.asUint8List());

    if (!isLastPacket) return null;

    try {
      final bytes = _pcmCache[srcId]!.toBytes();
      _pcmCache.remove(srcId);
      final filePath = await _saveWav(srcId, bytes);

      // 保存数据库 & 未读计数
      await _insertToDatabase(srcId: srcId, wavPath: filePath, isMine: isMine);

      return filePath;
    } catch (e) {
      debugPrint('❌ 合并/保存 WAV 失败: $e');
      return null;
    }
  }

  // ---------------- internal helpers ---------------- //

  Future<String> _saveWav(int srcId, Uint8List pcmBytes) async {
    final dir = await getApplicationDocumentsDirectory();
    final fileName =
        'voice_${srcId}_${DateTime.now().millisecondsSinceEpoch}.wav';
    final filePath = p.join(dir.path, fileName);

    final wavData = _buildWav(
      pcmBytes,
      sampleRate: 8000,
      channels: 1,
      bitsPerSample: 16,
    );

    await File(filePath).writeAsBytes(wavData, flush: true);
    debugPrint('💾 WAV 已保存: $filePath');
    return filePath;
  }

  Uint8List _buildWav(
    Uint8List pcmBytes, {
    required int sampleRate,
    required int channels,
    required int bitsPerSample,
  }) {
    final byteRate = sampleRate * channels * bitsPerSample ~/ 8;
    final blockAlign = channels * bitsPerSample ~/ 8;
    final dataLen = pcmBytes.length;
    final fileLen = dataLen + 36;

    final builder = BytesBuilder();

    void writeString(String s) => builder.add(utf8.encode(s));
    void writeUint32LE(int v) => builder.add([
      v & 0xFF,
      (v >> 8) & 0xFF,
      (v >> 16) & 0xFF,
      (v >> 24) & 0xFF,
    ]);
    void writeUint16LE(int v) => builder.add([v & 0xFF, (v >> 8) & 0xFF]);

    writeString('RIFF');
    writeUint32LE(fileLen);
    writeString('WAVE');
    writeString('fmt ');
    writeUint32LE(16);
    writeUint16LE(1);
    writeUint16LE(channels);
    writeUint32LE(sampleRate);
    writeUint32LE(byteRate);
    writeUint16LE(blockAlign);
    writeUint16LE(bitsPerSample);
    writeString('data');
    writeUint32LE(dataLen);
    builder.add(pcmBytes);

    return builder.toBytes();
  }

  // ---------------- database helpers ---------------- //

  Future<void> _insertToDatabase({
    required int srcId,
    required String wavPath,
    required bool isMine,
  }) async {
    String? groupId;

    debugPrint('🎤 [VoiceWavSaver] 语音来源: srcId=$srcId, isMine=$isMine');

    if (isMine) {
      // 发送的语音消息：使用当前激活的会话ID
      groupId = ConversationManager.currentConversationId.value;
      debugPrint('🎤 [VoiceWavSaver] 发送语音，当前激活会话ID: $groupId');

      if (groupId == null || groupId.isEmpty) {
        debugPrint('❌ [VoiceWavSaver] 发送语音时无有效的激活会话，抛弃语音消息');
        return;
      }
    } else {
      // 接收的语音消息：使用最后激活的会话ID，如果没有则使用当前激活的会话ID
      groupId =
          ConversationManager.lastConversationId.value ??
          ConversationManager.currentConversationId.value;
      debugPrint('🎤 [VoiceWavSaver] 接收语音，目标会话ID: $groupId');

      if (groupId == null || groupId.isEmpty) {
        debugPrint('❌ [VoiceWavSaver] 接收语音时无有效的目标会话，抛弃语音消息');
        return;
      }
    }

    debugPrint('✅ [VoiceWavSaver] 语音消息保存到会话: $groupId');

    final db = await DatabaseService.instance.database;
    final nowMs = DateTime.now().millisecondsSinceEpoch;

    await db.insert('group_messages', {
      'group_id': groupId,
      'src_id': srcId,
      'message_type': TK8620DataType.voice,
      'content': wavPath,
      'is_mine': isMine ? 1 : 0,
      'created_at': nowMs,
    });

    // 更新会话表
    final currentConversation = ConversationManager.currentConversationId.value;
    final unreadCount = (isMine || currentConversation == groupId) ? 0 : 1;

    await db.insert('group_conversations', {
      'conversation_id': groupId,
      'group_id': groupId,
      'unread_count': unreadCount,
      'last_msg_time': nowMs ~/ 1000,
    }, conflictAlgorithm: ConflictAlgorithm.replace);

    DatabaseService.groupChangedNotifier.value++;

    // 标记通话记录：收到或发送语音消息都算作通话
    if (isMine) {
      // 发送语音消息
      await CallHistoryStorage.instance.markPttSent(groupId);
    } else {
      // 接收语音消息
      await CallHistoryStorage.instance.markPttReceived(groupId);
    }
  }
}
