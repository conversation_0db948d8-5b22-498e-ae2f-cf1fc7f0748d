import 'dart:async';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';

// PCM 低延迟播放
import 'package:flutter_pcm_sound/flutter_pcm_sound.dart';

// ===== 新增依赖 =====
import '../bluetooth/bluetooth_manager.dart';
import '../device/device_manager.dart';
import '../protocol/tk8620_request_sender.dart';
import 'voice_wav_saver.dart';

import 'audio_codec.dart';
import 'melp_codec.dart';
import 'multi_frame_voice_assembler.dart';

/// 语音消息处理器：提供录音、编码、解码和回放功能
///
/// 修改后的流程:
/// 1. 开始录音 - 仅录制PCM数据
/// 2. 停止录音 - 获取原始PCM数据
/// 3. 编码 - 使用MELP 1.2kbps编码PCM数据
/// 4. 解码 - 解码MELP数据
/// 5. 播放 - 播放解码后的音频
class VoiceMessageProcessor {
  /// 创建语音消息处理器实例
  static Future<VoiceMessageProcessor> create() async {
    // 使用 MELP 1.2kbps 编解码器，8kHz采样率
    final codec = MelpCodecFactory.create1200();
    await codec.init();

    return VoiceMessageProcessor._(codec);
  }

  VoiceMessageProcessor._(this._codec);

  final AudioCodec _codec;
  final _recorder = AudioRecorder();
  final _audioPlayer = AudioPlayer();
  // flutter_pcm_sound 初始化标志
  bool _pcmPlayerReady = false;
  // 流式录音相关
  StreamSubscription<Uint8List>? _recordStreamSub; // 录音流订阅
  final List<int> _pcmBuffer = []; // PCM 缓冲区（采样点）
  // 发送前的 MELP 帧缓冲
  final List<Uint8List> _encodedFrameBuffer = [];
  static const int _framesPerSend = 6; // 每 10 帧一起发送
  bool _isStreaming = false; // 是否处于流式录音模式
  bool _isRecording = false;
  bool _isProcessing = false;
  // 当前发送者短地址（SrcID），用于本地语音存储
  int _currentSrcId = 0x01;

  // 录音状态变化控制器
  final _isRecordingController = StreamController<bool>.broadcast();

  /// 获取录音状态变化的流
  Stream<bool> get isRecordingChange => _isRecordingController.stream;

  // ================== 流式录音接口 ==================

  /// 开始流式录音。
  /// autoPlay 为 true 时会在本地即时解码并播放录音内容。
  Future<void> startStreamingRecording({bool autoPlay = false}) async {
    if (_isRecording) return;

    // 检查录音权限
    final hasPermission = await _recorder.hasPermission();
    if (!hasPermission) {
      debugPrint('❌ 录音权限被拒绝');
      return;
    }

    // 开启录音流
    final stream = await _recorder.startStream(
      const RecordConfig(
        encoder: AudioEncoder.pcm16bits,
        sampleRate: _sampleRate,
        numChannels: _channels,
      ),
    );

    _isRecording = true;
    _isStreaming = true;
    _isRecordingController.add(_isRecording);

    debugPrint('✅ 开始流式录音');

    // 记录当前 SrcID（DeviceID 低 8 位，默认 0x01）
    _currentSrcId = 0x01;
    final deviceIdHex = DeviceManager.instance.deviceIdNotifier.value;
    if (deviceIdHex != null && deviceIdHex.startsWith('0x')) {
      try {
        _currentSrcId = int.parse(deviceIdHex.substring(2), radix: 16) & 0xFF;
      } catch (_) {
        // ignore parse error, keep default 0x01
      }
    }

    // 监听录音流
    _recordStreamSub = stream.listen((Uint8List data) async {
      // 若录音已停止，直接丢弃后续数据
      if (!_isRecording) return;

      // 将字节数据转为 Int16 采样点并放入缓冲区
      final samples = _convertBytesToInt16List(data);
      _pcmBuffer.addAll(samples);

      // 按帧大小处理数据
      while (_pcmBuffer.length >= _frameSamples) {
        final frameSamples = _pcmBuffer.sublist(0, _frameSamples);
        _pcmBuffer.removeRange(0, _frameSamples);

        final frame = Int16List.fromList(frameSamples);

        try {
          // 如果录音已停止，则跳出处理循环
          if (!_isRecording) break;

          // 编码
          final packet = _codec.encode(frame);

          // 将 PCM 帧写入本地缓存，稍后合并生成 WAV
          await VoiceWavSaver.instance.handleVoice(
            srcId: _currentSrcId,
            pcm: frame,
            isLastPacket: false,
            isMine: true,
          );

          // 累积编码帧
          _encodedFrameBuffer.add(packet);

          // 只要缓冲区里达到 _framesPerSend，就分批发送
          while (_encodedFrameBuffer.length >= _framesPerSend) {
            // 取出指定数量的帧
            final framesToSend = _encodedFrameBuffer
                .take(_framesPerSend)
                .toList();

            // 使用多帧组装器打包数据
            final assembler = MultiFrameVoiceAssembler.instance;
            final packedData = assembler.packFrames(framesToSend);

            // 先移除已准备发送的帧，再进行 await 调用，避免并发情况下列表被外部清空后 RangeError
            if (_encodedFrameBuffer.length >= _framesPerSend) {
              _encodedFrameBuffer.removeRange(0, _framesPerSend);
            }

            // 异步发送打包后的语音数据
            await _trySendVoice(packedData, isLastPacket: false);
          }

          // 解码回放
          if (autoPlay) {
            final decodedFrame = _codec.decode(packet);
            await _ensurePcmPlayer();
            FlutterPcmSound.feed(PcmArrayInt16.fromList(decodedFrame));
          }
        } catch (e) {
          debugPrint('❌ 流式编码/解码失败: $e');
        }
      }
    });
  }

  /// 停止流式录音。
  Future<void> stopStreamingRecording() async {
    if (!_isRecording || !_isStreaming) return;

    // 先将录音标志置为 false，阻止 listener 继续处理剩余帧
    _isRecording = false;
    _isRecordingController.add(_isRecording);

    await _recordStreamSub?.cancel();
    _recordStreamSub = null;

    // 将剩余未发送的帧一次性发送，并标记为最后一包
    if (_encodedFrameBuffer.isNotEmpty) {
      // 使用多帧组装器打包剩余的帧
      final assembler = MultiFrameVoiceAssembler.instance;
      final packedData = assembler.packFrames(_encodedFrameBuffer);
      await _trySendVoice(packedData, isLastPacket: true);
      _encodedFrameBuffer.clear();
    } else {
      // 如果没有剩余数据，也需要发送一个空包告知结束
      await _trySendVoice(Uint8List(0), isLastPacket: true);
    }

    await _recorder.stop();

    // 告知 VoiceWavSaver 当前语音已结束，触发 WAV 合并与数据库写入
    await VoiceWavSaver.instance.handleVoice(
      srcId: _currentSrcId,
      pcm: Int16List(0),
      isLastPacket: true,
      isMine: true,
    );

    _isStreaming = false;
    _pcmBuffer.clear();

    _isRecordingController.add(_isRecording);

    debugPrint('✅ 已停止流式录音');
  }

  /// 立即停止流式录音，不发送剩余数据（用于实时通话挂断）
  Future<void> stopStreamingRecordingImmediately() async {
    if (!_isRecording || !_isStreaming) return;

    // 先将录音标志置为 false，阻止 listener 继续处理剩余帧
    _isRecording = false;
    _isRecordingController.add(_isRecording);

    await _recordStreamSub?.cancel();
    _recordStreamSub = null;

    // 直接清空缓冲区，不发送剩余数据
    _encodedFrameBuffer.clear();

    await _recorder.stop();

    _isStreaming = false;
    _pcmBuffer.clear();

    _isRecordingController.add(_isRecording);

    debugPrint('✅ 已立即停止流式录音（不发送剩余数据）');
  }

  /// 将原始字节数据转换为Int16List
  Int16List _convertBytesToInt16List(Uint8List bytes) {
    final Int16List result = Int16List(bytes.length ~/ 2);
    for (int i = 0; i < result.length; i++) {
      // 小端字节序 (Little Endian)
      result[i] = bytes[i * 2] | (bytes[i * 2 + 1] << 8);
    }
    return result;
  }

  /// 释放资源
  Future<void> dispose() async {
    await _recordStreamSub?.cancel();
    await _recorder.dispose();
    await _audioPlayer.dispose();
    await _codec.dispose();
    // 释放 flutter_pcm_sound 内部原生资源
    FlutterPcmSound.release();
    await _isRecordingController.close();
  }

  /// 当前是否正在录音
  bool get isRecording => _isRecording;

  /// 当前是否正在处理音频
  bool get isProcessing => _isProcessing;

  // ---------------- 私有辅助方法 ---------------- //

  /// 确保 FlutterPcmSound 已完成初始化
  Future<void> _ensurePcmPlayer() async {
    if (_pcmPlayerReady) return;
    await FlutterPcmSound.setup(
      sampleRate: _sampleRate,
      channelCount: _channels,
    );
    // 让播放器在收到 4 帧（≈80 ms）数据后开始播放，可按需调整
    FlutterPcmSound.setFeedThreshold(_frameSamples * 4);
    _pcmPlayerReady = true;
  }

  /// 尝试通过 TK8620 协议发送语音数据。
  /// 若未连接设备或尚未解析到 DeviceID，则打印警告并跳过。
  Future<void> _trySendVoice(
    Uint8List encodedData, {
    bool isLastPacket = false,
  }) async {
    // 打印编码后的语音数据长度，便于调试
    final device = BluetoothManager.currentDevice.value;
    if (device == null) {
      debugPrint('⚠️ 未连接设备，无法发送语音数据');
      return;
    }

    // 解析 SrcID（取 DeviceID 低 8 位，默认为 1）
    int srcId = 0x01;
    final deviceIdHex = DeviceManager.instance.deviceIdNotifier.value;
    if (deviceIdHex != null && deviceIdHex.startsWith('0x')) {
      try {
        srcId = int.parse(deviceIdHex.substring(2), radix: 16) & 0xFF;
      } catch (_) {}
    }

    try {
      await TK8620RequestSender.sendVoiceData(
        device,
        audioData: encodedData,
        srcId: srcId,
        isLastPacket: isLastPacket,
      );
    } catch (e) {
      debugPrint('❌ 发送语音数据失败: $e');
    }
  }

  // 编解码参数常量
  static const int _sampleRate = 8000; // 采样率 8 kHz（窄带）
  static const int _channels = 1; // 单声道
  // MELP 1.2kbps 使用 540 样本的帧（67.5ms）
  static const int _frameSamples = 540;
}
