import 'dart:async';
import 'dart:typed_data';

import 'package:flutter_pcm_sound/flutter_pcm_sound.dart';

import 'audio_codec.dart';
import 'melp_codec.dart';
import 'call_audio_session_manager.dart';
import 'multi_frame_voice_assembler.dart';

/// Remote voice player
///
/// This helper focuses on **playback only** – it takes MELP encoded packets
/// coming from TK8620 voice frames, decodes them and feeds PCM samples to
/// `flutter_pcm_sound` to achieve low-latency audio output on the *receiving*
/// side.
class VoicePlayer {
  VoicePlayer._(this._codec);

  final AudioCodec _codec;

  bool _pcmReady = false;

  // ---------------- factory & lifecycle ---------------- //

  static Future<VoicePlayer> create() async {
    // 使用 MELP 1.2kbps 编解码器，与发送端匹配
    final codec = MelpCodecFactory.create1200();
    await codec.init();
    return VoicePlayer._(codec);
  }

  /// 解码并（可选）播放一帧 MELP 数据。
  /// 返回解码后的 PCM (`Int16List`)，若解码失败则返回 `null`。
  /// [playAudio] 控制是否立即播放，默认 `true`。
  Future<Int16List?> playFrame(
    Uint8List encoded, {
    bool playAudio = true,
  }) async {
    try {
      final Int16List pcm = _codec.decode(encoded);

      if (playAudio) {
        // 确保在通话模式下，音频通过听筒播放
        if (CallAudioSessionManager.instance.isCallModeActive) {
          await _ensurePcmPlayer();
          FlutterPcmSound.feed(PcmArrayInt16.fromList(pcm));
        } else {
          // 非通话模式下的正常播放
          await _ensurePcmPlayer();
          FlutterPcmSound.feed(PcmArrayInt16.fromList(pcm));
        }
      }

      return pcm;
    } catch (e) {
      // Ignore single-frame errors but keep a log for debugging
      // ignore: avoid_print
      print('❌ VoicePlayer playFrame failed: $e');
      return null;
    }
  }

  /// 解码并播放多帧MELP数据包
  ///
  /// [packedData] 包含多帧MELP数据的数据包
  /// [playAudio] 控制是否立即播放，默认 `true`
  /// 返回解码后的PCM数据列表
  Future<List<Int16List>> playMultiFramePacket(
    Uint8List packedData, {
    bool playAudio = true,
  }) async {
    try {
      // 使用多帧组装器解码数据包
      final assembler = MultiFrameVoiceAssembler.instance;
      final pcmFrames = await assembler.decodePackedFrames(
        packedData,
        _codec as MelpAudioCodec,
      );

      if (playAudio && pcmFrames.isNotEmpty) {
        await _ensurePcmPlayer();

        // 逐帧播放PCM数据
        for (int i = 0; i < pcmFrames.length; i++) {
          FlutterPcmSound.feed(PcmArrayInt16.fromList(pcmFrames[i]));
          // 可以在这里添加小的延迟来控制播放节奏，如果需要的话
          // await Future.delayed(const Duration(milliseconds: 1));
        }

        // ignore: avoid_print
        print('✅ 播放多帧数据包成功，共${pcmFrames.length}帧');
      }

      return pcmFrames;
    } catch (e) {
      // ignore: avoid_print
      print('❌ VoicePlayer playMultiFramePacket failed: $e');
      return [];
    }
  }

  Future<void> dispose() async {
    await _codec.dispose();
    FlutterPcmSound.release();
  }

  // ---------------- internal helpers ---------------- //

  Future<void> _ensurePcmPlayer() async {
    if (_pcmReady) return;
    await FlutterPcmSound.setup(
      sampleRate: _sampleRate,
      channelCount: _channels,
    );
    FlutterPcmSound.setFeedThreshold(_frameSamples * 4);
    _pcmReady = true;
  }

  // Keep these constants in sync with the encoder side
  // MELP 1.2kbps: 540 samples per frame at 8kHz (67.5ms frame)
  static const int _sampleRate = 8000;
  static const int _channels = 1;
  static const int _frameSamples = 540; // MELP 1.2kbps frame size
}
