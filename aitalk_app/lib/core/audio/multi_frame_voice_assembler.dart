import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'melp_codec.dart';

/// 多帧语音数据组装器
/// 
/// 专门处理每5帧MELP数据打包发送的场景：
/// 1. 发送端：将5个MELP编码帧合并为一个数据包发送
/// 2. 接收端：将接收到的数据包拆分为5个MELP帧，逐帧解码播放
class MultiFrameVoiceAssembler {
  MultiFrameVoiceAssembler._();
  
  static final MultiFrameVoiceAssembler instance = MultiFrameVoiceAssembler._();
  
  // MELP 1.2kbps 每帧编码后的字节数
  static const int _melpFrameSize = 11;
  // 每包包含的帧数（默认值，实际可以动态调整）
  static const int _framesPerPacket = 6;
  // 支持的最大帧数（放宽限制）
  static const int _maxFramesPerPacket = 20;
  // 每包的总字节数（基于默认帧数）
  static const int _packetSize = _melpFrameSize * _framesPerPacket; // 110字节
  
  /// 将多个MELP编码帧合并为一个数据包
  /// 
  /// [encodedFrames] 编码后的MELP帧列表，每帧11字节
  /// 返回合并后的数据包
  Uint8List packFrames(List<Uint8List> encodedFrames) {
    if (encodedFrames.isEmpty) {
      debugPrint('⚠️ 没有帧数据需要打包');
      return Uint8List(0);
    }
    
    // 验证每帧的大小
    for (int i = 0; i < encodedFrames.length; i++) {
      if (encodedFrames[i].length != _melpFrameSize) {
        debugPrint('❌ 第${i + 1}帧大小不正确: ${encodedFrames[i].length}字节，期望$_melpFrameSize字节');
        throw ArgumentError('MELP帧大小不正确');
      }
    }
    
    final packedData = BytesBuilder();
    
    // 添加帧数量标识（1字节）
    packedData.addByte(encodedFrames.length);
    
    // 依次添加每帧数据
    for (final frame in encodedFrames) {
      packedData.add(frame);
    }
    
    final result = packedData.toBytes();
    debugPrint('📦 打包${encodedFrames.length}帧MELP数据，总大小: ${result.length}字节');
    
    return result;
  }
  
  /// 将数据包拆分为多个MELP编码帧
  /// 
  /// [packedData] 打包的数据
  /// 返回拆分后的MELP帧列表
  List<Uint8List> unpackFrames(Uint8List packedData) {
    if (packedData.isEmpty) {
      debugPrint('⚠️ 接收到空数据包');
      return [];
    }
    
    if (packedData.isEmpty) {
      debugPrint('❌ 数据包太小，无法解析帧数量');
      return [];
    }
    
    // 读取帧数量
    final frameCount = packedData[0];
    final expectedSize = 1 + frameCount * _melpFrameSize;
    
    if (packedData.length != expectedSize) {
      debugPrint('❌ 数据包大小不匹配: 实际${packedData.length}字节，期望$expectedSize字节');
      return [];
    }
    
    final frames = <Uint8List>[];
    
    // 从第1字节开始，每11字节为一帧
    for (int i = 0; i < frameCount; i++) {
      final startIndex = 1 + i * _melpFrameSize;
      final endIndex = startIndex + _melpFrameSize;
      
      if (endIndex <= packedData.length) {
        final frame = packedData.sublist(startIndex, endIndex);
        frames.add(frame);
      } else {
        debugPrint('❌ 第${i + 1}帧数据不完整');
        break;
      }
    }
    
    debugPrint('📦 解包得到${frames.length}帧MELP数据');
    return frames;
  }
  
  /// 解码多帧数据并返回PCM音频
  /// 
  /// [packedData] 打包的MELP数据
  /// [codec] MELP解码器
  /// 返回解码后的PCM数据列表，每个元素对应一帧
  Future<List<Int16List>> decodePackedFrames(
    Uint8List packedData,
    MelpAudioCodec codec,
  ) async {
    final frames = unpackFrames(packedData);
    final pcmFrames = <Int16List>[];
    
    for (int i = 0; i < frames.length; i++) {
      try {
        final pcm = codec.decode(frames[i]);
        pcmFrames.add(pcm);
        debugPrint('✅ 解码第${i + 1}帧成功，PCM长度: ${pcm.length}');
      } catch (e) {
        debugPrint('❌ 解码第${i + 1}帧失败: $e');
        // 继续处理其他帧，不中断整个解码过程
      }
    }
    
    return pcmFrames;
  }
  
  /// 将多个PCM帧合并为连续的音频数据
  /// 
  /// [pcmFrames] PCM帧列表
  /// 返回合并后的PCM数据
  Int16List mergePcmFrames(List<Int16List> pcmFrames) {
    if (pcmFrames.isEmpty) {
      return Int16List(0);
    }
    
    final totalLength = pcmFrames.fold<int>(0, (sum, frame) => sum + frame.length);
    final merged = Int16List(totalLength);
    
    int offset = 0;
    for (final frame in pcmFrames) {
      merged.setRange(offset, offset + frame.length, frame);
      offset += frame.length;
    }
    
    debugPrint('🔗 合并${pcmFrames.length}帧PCM数据，总长度: ${merged.length}样本');
    return merged;
  }
  
  /// 获取每包的帧数
  static int get framesPerPacket => _framesPerPacket;

  /// 获取支持的最大帧数
  static int get maxFramesPerPacket => _maxFramesPerPacket;

  /// 获取MELP帧大小
  static int get melpFrameSize => _melpFrameSize;

  /// 获取每包的字节大小
  static int get packetSize => _packetSize;
}
