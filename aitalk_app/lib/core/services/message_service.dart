import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

import '../protocol/di_data_dispatcher.dart';
import '../protocol/tk8620_frame_decoder.dart';
import '../protocol/tk8620_protocol.dart';
import 'channel_manager.dart';
import 'database_service.dart';
import 'conversation_manager.dart';
import 'notification_service.dart';
import '../utils/group_util.dart';

/// 全局消息持久化服务。
///
/// 在应用启动时初始化一次 (`MessageService.instance;`)，
/// 持续监听 `DiDataDispatcher` 的 `messages` 流，将收到的
/// TK8620 数据写入本地 SQLite，以保证即使 UI 不在前台也
/// 不会丢失消息。
class MessageService {
  MessageService._() {
    _sub = DiDataDispatcher.instance.messages.listen(_onMessage);
  }

  static final MessageService instance = MessageService._();

  late final StreamSubscription<TK8620Message> _sub;

  Future<void> _onMessage(TK8620Message message) async {
    debugPrint('[MessageService] 收到消息: ${message.toString()}');

    // 处理所有文本数据，不仅仅是公共群
    if (message.payload is! TK8620TextData) {
      debugPrint('[MessageService] 跳过非文本消息: ${message.payload.runtimeType}');
      return;
    }

    final int dst = message.frame.dstId;
    final text = (message.payload as TK8620TextData).text;
    final srcId = message.frame.srcId;

    debugPrint(
      '[MessageService] 处理文本消息: dst=0x${dst.toRadixString(16).padLeft(8, '0')}, src=$srcId, text="$text"',
    );

    // 先获取数据库实例
    final db = await DatabaseService.instance.database;

    // 判断消息类型并确定目标群组
    String? groupId;

    // 公共广播或公共群定向消息
    const int minPubId = 0x10000001;
    const int maxPubId = 0x10000010;
    final bool isBroadcast = dst == 0xFFFFFFFF;
    final bool isPublicGroupMsg = dst >= minPubId && dst <= maxPubId;

    if (isBroadcast || isPublicGroupMsg) {
      // 公共群消息处理逻辑（保持原有逻辑）
      int channel;
      if (isBroadcast) {
        channel = ChannelManager.currentChannel.value;
        groupId = GroupUtil.publicGroupId(channel);
      } else {
        final hex = dst.toRadixString(16).padLeft(8, '0').toUpperCase();
        channel =
            GroupUtil.channelFromGroupId(hex) ??
            ChannelManager.currentChannel.value;
        groupId = GroupUtil.publicGroupId(channel);
      }
    } else {
      // 私有群消息：dstId就是私有群ID（不带0x前缀，与其他地方保持一致）
      groupId = dst.toRadixString(16).padLeft(8, '0').toUpperCase();

      debugPrint('[MessageService] 识别为私有群消息: groupId=$groupId');

      // 确保私有群记录存在于 groups 表中
      await _ensurePrivateGroupExists(db, groupId);
    }

    debugPrint('[MessageService] 最终确定群组: $groupId');

    // 确保发送者在群成员列表中（对所有群组类型都适用）
    await DatabaseService.instance.ensureGroupMember(groupId, srcId);

    final nowMs = DateTime.now().millisecondsSinceEpoch;

    try {
      debugPrint('[MessageService] 开始写入消息到数据库...');

      await db.insert('group_messages', {
        'group_id': groupId,
        'src_id': srcId,
        'message_type': TK8620DataType.text,
        'content': text,
        'is_mine': 0,
        'created_at': nowMs,
      });

      debugPrint('[MessageService] 消息已写入 group_messages 表');

      // 计算未读数
      int unread = 0;
      final exist = await db.query(
        'group_conversations',
        columns: ['unread_count'],
        where: 'conversation_id = ?',
        whereArgs: [groupId],
        limit: 1,
      );
      if (exist.isNotEmpty) {
        unread = (exist.first['unread_count'] as int?) ?? 0;
      }

      debugPrint('[MessageService] 当前未读数: $unread');

      // 若当前界面不是该会话，则未读数 +1
      final currentConversation =
          ConversationManager.currentConversationId.value;
      debugPrint('[MessageService] 当前会话: $currentConversation, 消息群组: $groupId');
      debugPrint(
        '[MessageService] 群组ID比较: "$currentConversation" == "$groupId" ? ${currentConversation == groupId}',
      );

      if (currentConversation != groupId) {
        unread += 1;
        debugPrint('[MessageService] 不在当前会话，未读数+1: $unread');
      } else {
        unread = 0; // 打开的会话不计未读
        debugPrint('[MessageService] 在当前会话，未读数清零');
      }

      await db.insert('group_conversations', {
        'conversation_id': groupId,
        'group_id': groupId,
        'unread_count': unread,
        'last_msg_time': nowMs ~/ 1000,
      }, conflictAlgorithm: ConflictAlgorithm.replace);

      debugPrint('[MessageService] 会话记录已更新，未读数: $unread');

      // 通知界面刷新
      final oldValue = DatabaseService.groupChangedNotifier.value;
      DatabaseService.groupChangedNotifier.value++;
      debugPrint(
        '[MessageService] 通知界面刷新: $oldValue -> ${DatabaseService.groupChangedNotifier.value}',
      );

      // 若未读数大于0，则尝试发送本地推送通知
      if (unread > 0) {
        debugPrint('[MessageService] 发送推送通知');
        NotificationService.instance.showGroupMessageNotification(
          groupId: groupId,
          message: text,
        );
      }

      debugPrint('[MessageService] 消息处理完成');
    } catch (e) {
      debugPrint('[MessageService] 写入消息失败: $e');
    }
  }

  void dispose() {
    _sub.cancel();
  }

  /// 确保私有群记录存在于 groups 表中
  Future<void> _ensurePrivateGroupExists(Database db, String groupId) async {
    final existing = await db.query(
      'groups',
      where: 'group_id = ?',
      whereArgs: [groupId],
      limit: 1,
    );

    if (existing.isEmpty) {
      final nowMs = DateTime.now().millisecondsSinceEpoch;
      await db.insert('groups', {
        'group_id': groupId,
        'group_name': '私有群 $groupId',
        'is_private': 1,
        'channel': 1, // 默认信道，可以后续更新
        'created_at': nowMs,
        'updated_at': nowMs,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);
    }
  }
}
