import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Handles persisting and retrieving simple UI preferences such as theme mode
/// and text scale factor.
class SettingsService {
  static const _keyThemeMode = 'ui_theme_mode';
  static const _keyTextScale = 'ui_text_scale';
  static const _keyLocale = 'ui_locale';
  // 是否自动播放语音
  static const _keyAutoPlayVoice = 'ui_auto_play_voice';
  // 通话音频设置
  static const _keyCallUseEarpieceMode = 'call_use_earpiece_mode';
  static const _keyCallEnableEchoCancellation = 'call_enable_echo_cancellation';
  static const _keyCallEnableNoiseSuppression = 'call_enable_noise_suppression';

  SettingsService._();

  /// Saves [mode] to persistent storage.
  static Future<void> saveThemeMode(ThemeMode mode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyThemeMode, mode.name);
  }

  /// Saves the chosen text [scale] (e.g. 1.0) to storage.
  static Future<void> saveTextScale(double scale) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(_keyTextScale, scale);
  }

  /// Reads stored theme mode, returns null if not set.
  static Future<ThemeMode?> loadThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final str = prefs.getString(_keyThemeMode);
    if (str == null) return null;
    return ThemeMode.values.firstWhere(
      (e) => e.name == str,
      orElse: () => ThemeMode.system,
    );
  }

  /// Reads stored text scale factor, returns null if not set.
  static Future<double?> loadTextScale() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getDouble(_keyTextScale);
  }

  /// Saves selected locale; null indicates follow system.
  static Future<void> saveLocale(Locale? locale) async {
    final prefs = await SharedPreferences.getInstance();
    if (locale == null) {
      await prefs.remove(_keyLocale);
    } else {
      await prefs.setString(_keyLocale, locale.languageCode);
    }
  }

  /// Loads stored locale. Returns null to indicate follow system or not set.
  static Future<Locale?> loadLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final code = prefs.getString(_keyLocale);
    if (code == null) return null;
    return Locale(code);
  }

  /// Saves whether to auto-play voice messages.
  static Future<void> saveAutoPlayVoice(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyAutoPlayVoice, enabled);
  }

  /// Loads stored auto-play voice preference. Returns null if not set.
  static Future<bool?> loadAutoPlayVoice() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyAutoPlayVoice);
  }

  // ==================== 通话音频设置 ====================

  /// 保存是否使用听筒模式
  static Future<void> saveCallUseEarpieceMode(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyCallUseEarpieceMode, enabled);
  }

  /// 获取是否使用听筒模式设置
  static Future<bool?> getCallUseEarpieceMode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyCallUseEarpieceMode);
  }

  /// 保存是否启用回音消除
  static Future<void> saveCallEnableEchoCancellation(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyCallEnableEchoCancellation, enabled);
  }

  /// 获取是否启用回音消除设置
  static Future<bool?> getCallEnableEchoCancellation() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyCallEnableEchoCancellation);
  }

  /// 保存是否启用噪音抑制
  static Future<void> saveCallEnableNoiseSuppression(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyCallEnableNoiseSuppression, enabled);
  }

  /// 获取是否启用噪音抑制设置
  static Future<bool?> getCallEnableNoiseSuppression() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyCallEnableNoiseSuppression);
  }
}
