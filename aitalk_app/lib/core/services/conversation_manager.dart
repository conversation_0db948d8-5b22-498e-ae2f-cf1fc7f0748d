import 'package:flutter/foundation.dart';

/// 用于追踪当前正在打开的会话（群组）。
/// 若没有任何聊天界面处于顶部，则为 null。
class ConversationManager {
  ConversationManager._();

  static final ValueNotifier<String?> currentConversationId =
      ValueNotifier<String?>(null);

  /// 最后一次进入的会话 ID，用于 UI 高亮。
  /// 在 `exit` 后仍保留，不影响未读计数逻辑。
  static final ValueNotifier<String?> lastConversationId =
      ValueNotifier<String?>(null);

  /// 进入会话
  static void enter(String conversationId) {
    currentConversationId.value = conversationId;
    lastConversationId.value = conversationId;
  }

  /// 离开会话
  static void exit() {
    currentConversationId.value = null;
  }
}
