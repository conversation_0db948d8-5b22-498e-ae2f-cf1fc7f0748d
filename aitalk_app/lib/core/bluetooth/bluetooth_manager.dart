import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter/foundation.dart';

import '../protocol/challenge_command.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import '../protocol/at_commands.dart';
import 'bluetooth_uuids.dart';
import '../protocol/at_response_handler.dart';
import '../protocol/device_control_protocol.dart';
import 'connection_keeper.dart';
import 'protocol_gatt_helper.dart';
import 'passthrough_gatt_helper.dart';
import '../protocol/device_control_response_parser.dart';
import '../protocol/device_control_request_sender.dart';
import 'last_device_storage.dart';
import 'auto_reconnector.dart';
import '../constants/rate_mode_payload.dart';
import '../protocol/work_mode_constants.dart';

// 同步一份密钥用于解密（与 at_commands.dart 保持一致）
const List<int> _xorKey = <int>[
  0x78,
  0x91,
  0x4A,
  0x18,
  0x8F,
  0xFA,
  0x16,
  0x23,
  0x5F,
  0xFC,
  0x33,
  0x35,
  0xEC,
  0xA7,
  0xC3,
  0x85,
];

List<int> _xor(List<int> data) {
  final keyLen = _xorKey.length;
  return List<int>.generate(data.length, (i) => data[i] ^ _xorKey[i % keyLen]);
}

class BluetoothManager {
  // 新增：全局连接设备通知器，UI 可监听以实时更新连接状态
  static final ValueNotifier<BluetoothDevice?> currentDevice =
      ValueNotifier<BluetoothDevice?>(null);

  // 保存每个已监听响应数据的设备，防止重复打印
  static Map<String, StreamSubscription<List<int>>> _respSubs = {};

  // 预先订阅协议帧，避免 UI 层还没监听就错过早期回包
  static Map<String, StreamSubscription<BTDeviceControlFrame>> _protocolSubs =
      {};

  BluetoothManager._();

  // Stream of scanning status
  static Stream<bool> get isScanning => FlutterBluePlus.isScanning;

  // Stream of scan results (current + previous)
  static Stream<List<ScanResult>> get scanResults =>
      FlutterBluePlus.scanResults;

  /// Start BLE scan with default 5-second timeout
  static Future<void> startScan({
    Duration timeout = const Duration(seconds: 5),
    bool androidUsesFineLocation = true,
    List<Guid> withServices = const [],
  }) async {
    await FlutterBluePlus.startScan(
      timeout: timeout,
      androidUsesFineLocation: androidUsesFineLocation,
      withServices: withServices,
    );
  }

  /// Stop an active BLE scan
  static Future<void> stopScan() => FlutterBluePlus.stopScan();

  /// Connect to [device] with a timeout
  static Future<void> connect(
    BluetoothDevice device, {
    Duration timeout = const Duration(seconds: 10),
    bool preSubscribeProtocolFrames = true,
  }) async {
    await device.connect(timeout: timeout);

    // 连接成功后通过通知器广播
    currentDevice.value = device;

    // 连接成功后，记录当前设备，便于下次自动重连
    // ⚠️ 这里只记录 remoteId 字符串（MAC/UUID），不会存储用户敏感数据
    await LastDeviceStorage.save(device.remoteId.toString());

    if (preSubscribeProtocolFrames) {
      // 预先订阅协议帧，避免 UI 层还没监听就错过早期回包
      final devKey = device.remoteId.toString();
      _protocolSubs ??= <String, StreamSubscription<BTDeviceControlFrame>>{};
      if (!_protocolSubs.containsKey(devKey)) {
        final stream = await ProtocolGattHelper.frameStream(device);
        final sub = stream.listen((frame) {
          // 预先订阅协议帧，避免 UI 层还没监听就错过早期回包
          // 这里可以添加日志记录或缓存逻辑
          debugPrint('BluetoothManager: 收到协议帧预订阅数据: ${frame.toString()}');
        });

        // 断开后清理
        device.cancelWhenDisconnected(sub);

        // 连接状态监听，移除记录
        StreamSubscription<BluetoothConnectionState>? discSub;
        discSub = device.connectionState.listen((s) {
          if (s == BluetoothConnectionState.disconnected) {
            _protocolSubs.remove(devKey);
            // 清理 PASSTHROUGH GATT 缓存，确保重新连接时重新发现服务
            PassthroughGattHelper.cleanup(device);
            // 新增: 如果当前 UI 记录的设备就是该设备, 清空通知器, 让界面感知断开
            if (currentDevice.value?.remoteId == device.remoteId) {
              currentDevice.value = null;
            }
            discSub?.cancel();
          }
        });

        _protocolSubs[devKey] = sub;
      }
    }
  }

  /// Disconnect from [device]
  static Future<void> disconnect(BluetoothDevice device) async {
    // 在真正断链之前先告知 ConnectionKeeper 这是"用户主动断开"，
    // 这样 ConnectionKeeper 不会把随后的 disconnected 事件误判为异常掉线而触发自动重连
    try {
      ConnectionKeeper.markManualDisconnect();
    } catch (_) {}

    await device.disconnect();
    // 主动断开后立即清理 PASSTHROUGH GATT 缓存，避免下次连接使用失效特征
    PassthroughGattHelper.cleanup(device);
    // 断开后清空通知器
    if (currentDevice.value?.remoteId == device.remoteId) {
      currentDevice.value = null;
    }
  }

  /// After a successful BLE connection, configure the 8620 with a default
  /// initialization sequence (query serial number & set default params).
  ///
  /// The sequence is:
  /// 1. Query serial number
  /// 2. Set all four frequencies to 483_600_000
  /// 3. Set rate mode to 5
  /// 4. Set BCNID to 0
  /// 5. Set additional bit (ADDTL) to 1
  static Future<void> initializeDevice(BluetoothDevice device) async {
    // 获取PASSTHROUGH服务的响应流并设置监听
    final responseStream = await PassthroughGattHelper.getResponseStream(
      device,
    );

    // ---------------------------------------------
    //   避免重复监听造成日志重复
    // ---------------------------------------------
    final devKey = device.remoteId.toString();
    _respSubs ??= <String, StreamSubscription<List<int>>>{};
    if (_respSubs.containsKey(devKey)) {
      // 已经监听过该设备，直接返回
    } else {
      final sub = responseStream.listen((data) {
        try {
          // 不再直接进行 UTF-8 解码，而是先检查前三个字节是否为 "+DI"
          final bool isPlainDi =
              data.length >= 3 &&
              data[0] == 0x2B /* '+' */ &&
              data[1] == 0x44 /* 'D' */ &&
              data[2] == 0x49 /* 'I' */;

          if (isPlainDi) {
            final msg = latin1.decode(data, allowInvalid: true);
            debugPrint(
              '[BluetoothManager] 识别为+DI消息: ${msg.substring(0, msg.length > 50 ? 50 : msg.length)}...',
            );
            AtResponseHandler.instance.handle(msg);
            return;
          }

          // 其它指令需做 XOR 解密后再 UTF-8 解码
          final decrypted = _xor(data);
          final msg = utf8.decode(decrypted, allowMalformed: true);
          debugPrint('[BluetoothManager] 解密后的消息: $msg');
          AtResponseHandler.instance.handle(msg);
        } catch (e) {
          debugPrint('⬅️  AT Response decode error: $e');
        }
      });

      // 断开后自动清理
      device.cancelWhenDisconnected(sub);

      // 当设备断开后移除 map 中的订阅记录，确保下次连接能重新监听
      // 监听连接状态以便在断开时移除订阅映射
      StreamSubscription<BluetoothConnectionState>? disconnectSub;
      disconnectSub = device.connectionState.listen((s) {
        if (s == BluetoothConnectionState.disconnected) {
          _respSubs.remove(devKey);
          disconnectSub?.cancel();
          // 新增: 如果当前 UI 记录的设备就是该设备, 清空通知器, 让界面感知断开
          if (currentDevice.value?.remoteId == device.remoteId) {
            currentDevice.value = null;
          }
        }
      });

      _respSubs[devKey] = sub;
    }

    Future<void> _writeCmd(
      AtCommandType type, {
      Map<String, dynamic>? params,
    }) async {
      final bytes = getAtCommandBytes(type, params: params);

      // 使用PassthroughGattHelper发送AT指令
      await PassthroughGattHelper.sendAtCommand(
        device,
        bytes,
        withoutResponse: true,
      );
      // 硬件侧处理 AT 指令需要时间，适当等待避免指令粘连
      await Future.delayed(const Duration(milliseconds: 150));
    }

    // -----------------------------------------------------------------
    // Step 0. Perform CHALLENGE handshake before querying DeviceID
    // -----------------------------------------------------------------
    Future<bool> _performChallenge() async {
      const int maxAttempts = 3;
      int attempt = 0;
      while (attempt < maxAttempts) {
        attempt++;
        // 1) 生成 challenge 数据
        final challengeBytes = ChallengeCommand.buildChallengeBytes();
        final hexStr = challengeBytes
            .map((b) => b.toRadixString(16).padLeft(2, '0'))
            .join()
            .toUpperCase();
        // 发送（已 XOR 加密）
        final bytes = getAtCommandBytes(
          AtCommandType.challenge,
          params: {'challengeHex': hexStr},
        );
        await PassthroughGattHelper.sendAtCommand(
          device,
          bytes,
          withoutResponse: true,
        );

        // 等待响应并验证
        final completer = Completer<bool>();
        late StreamSubscription subCh;
        subCh = responseStream.listen((data) {
          final decrypted = _xor(data);
          final msg = utf8.decode(decrypted, allowMalformed: true);
          final lines = msg.split('\r');
          final parsed = ChallengeCommand.parseReplyLines(lines);
          if (parsed.sig != null) {
            final ok = ChallengeCommand.verifySignature(
              parsed.sig!,
              challengeBytes,
            );
            if (ok) {
              debugPrint('✅ CHALLENGE verify OK (attempt $attempt)');
              completer.complete(true);
            } else {
              debugPrint('❌ CHALLENGE verify FAILED (attempt $attempt)');
              completer.complete(false);
            }
            subCh.cancel();
          }
        });
        try {
          final success = await completer.future.timeout(
            const Duration(seconds: 2),
          );
          if (success) {
            return true;
          }
        } catch (_) {
          debugPrint('⚠️  CHALLENGE verification timeout (attempt $attempt)');
        } finally {
          await subCh.cancel();
        }
      }
      return false;
    }

    final verified = await _performChallenge();
    if (!verified) {
      throw Exception('CHALLENGE verification failed after retries');
    }

    // 1. Serial number query --> 需要等待 DeviceID 解析完成
    final deviceIdCompleter = Completer<void>();
    late StreamSubscription subId;
    subId = AtResponseHandler.instance.responses.listen((r) {
      if (r.type == AtResponseType.deviceId) {
        deviceIdCompleter.complete();
        subId.cancel();
      }
    });

    // 持续发送查询指令，直到解析到 DeviceID
    while (!deviceIdCompleter.isCompleted) {
      await _writeCmd(AtCommandType.serialNumberQuery);
      // 每次发送后等待 800ms，看是否收到响应；未收到则继续循环
      try {
        await deviceIdCompleter.future.timeout(
          const Duration(milliseconds: 800),
        );
      } catch (_) {
        // timeout, loop again
      }
    }

    // 1.b Query TurMass firmware version after DeviceID 已取得
    await _writeCmd(AtCommandType.versionQuery);

    // 2. Default frequency (all 483600000)
    await _writeCmd(
      AtCommandType.setFreq,
      params: {
        'txDataFreq': 483600000,
        'rxDataFreq': 483600000,
        'txBcnFreq': 483600000,
        'rxBcnFreq': 483600000,
      },
    );

    // 3. Default rate mode 5
    const int defaultRate = 6;
    await _writeCmd(AtCommandType.setRate, params: {'rateMode': defaultRate});
    // 更新全局速率配置
    RateModePayload.setCurrentRateMode(defaultRate);

    // 4. Default BCNID 0
    await _writeCmd(AtCommandType.setBcnId, params: {'bcnID': 0});

    // 5. Additional bit (ADDTL) 1
    await _writeCmd(AtCommandType.setAddtl, params: {'mode': 1});

    // 6. Default work mode (异步收发工作模式)
    await _writeCmd(
      AtCommandType.workMode,
      params: {'mode': WorkMode.asyncTransceive},
    );
    debugPrint(
      '✅ 初始化工作模式配置完成: ${WorkMode.getDescription(WorkMode.asyncTransceive)}',
    );
  }

  /// Ensure plugin is initialized early (call at app startup)
  static Future<void> ensureInitialized() async {
    try {
      // First call to any method initializes the platform channel
      await FlutterBluePlus.adapterStateNow;
    } catch (_) {}
  }

  /// ------------------------------
  ///   自动重连相关
  /// ------------------------------

  /// App 启动时调用，尝试重连上一次连接过的设备。
  /// 若成功连接，返回对应 [BluetoothDevice]；失败则返回 null。
  ///
  /// 该方法内部会：
  /// 1. 读取 SharedPreferences 中保存的 deviceId；
  /// 2. 等待蓝牙适配器开启（如用户尚未打开蓝牙则继续监听）；
  /// 3. 最多进行 [maxRetries] 轮扫描并尝试连接，每轮扫描时长 [scanTimeout]；
  /// 4. 连接成功即返回，失败则继续下一轮，全部失败后返回 null。
  static Future<BluetoothDevice?> tryReconnectOnStartup({
    int maxRetries = 5,
    Duration scanTimeout = const Duration(seconds: 4),
    Duration retryDelay = const Duration(seconds: 2),
  }) async {
    // 为兼容旧代码，直接调用新模块 AutoReconnector，避免逻辑重复
    return AutoReconnector.tryReconnect(
      maxRetries: maxRetries,
      scanTimeout: scanTimeout,
      retryDelay: retryDelay,
    );
  }

  /// 扫描寻找指定 remoteId 的设备；找到则返回对应 [BluetoothDevice]，否则返回 null
  static Future<BluetoothDevice?> _scanForDevice(
    String targetId,
    Duration scanTimeout,
  ) async {
    // 0. 检查系统层已连接的 BLE 设备
    final current = await FlutterBluePlus.connectedDevices;
    for (final d in current) {
      if (d.remoteId.toString() == targetId) {
        return d; // 已经连接/系统已配对
      }
    }

    // 0.b 进一步检查“系统已知”但未被当前 App 连接的设备（iOS 可能已自动连接后台外设）
    try {
      final sys = await FlutterBluePlus.systemDevices([
        PASSTHROUGH_SERVICE_UUID,
      ]);
      for (final d in sys) {
        if (d.remoteId.toString() == targetId) {
          return d; // 已被系统连接/缓存
        }
      }
    } catch (_) {
      // 某些平台可能不支持 systemDevices，忽略错误继续扫描
    }

    BluetoothDevice? foundDevice;

    // 监听扫描结果
    late final StreamSubscription<List<ScanResult>> resultSub;
    final completer = Completer<void>();

    resultSub = scanResults.listen((results) {
      for (final r in results) {
        if (r.device.remoteId.toString() == targetId) {
          foundDevice = r.device;
          if (!completer.isCompleted) {
            completer.complete();
          }
          break;
        }
      }
    });

    // 开始扫描
    await startScan(timeout: scanTimeout);

    // 等待扫描完成或找到目标
    try {
      await completer.future.timeout(scanTimeout);
    } catch (_) {
      // timeout ignore
    }

    await resultSub.cancel();
    // 仅返回设备，连接与初始化由调用方处理
    return foundDevice;
  }

  /// 等待蓝牙适配器处于 ON 状态
  static Future<void> _waitUntilBluetoothOn() async {
    var state = await FlutterBluePlus.adapterStateNow;
    if (state == BluetoothAdapterState.on) return;

    final completer = Completer<void>();
    late final StreamSubscription sub;
    sub = FlutterBluePlus.adapterState.listen((s) {
      if (s == BluetoothAdapterState.on) {
        completer.complete();
        sub.cancel();
      }
    });
    await completer.future;
  }
}
