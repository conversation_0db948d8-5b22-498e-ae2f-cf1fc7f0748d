// TK8620 射频通信协议核心定义
// 参考规范: aiTalk SDD §4.1.3.1 & §4.1.3.2
// 该文件定义了TK8620射频通信协议的核心数据结构和常量

import 'dart:typed_data';

/// TK8620协议帧类型，对应FrameCtrl字段的3:2位
/// 00: 会话帧, 01: 命令帧, 10: 数据帧, 11: 语音帧
enum TK8620FrameType {
  session(0), // 会话帧
  command(1), // 命令帧
  data(2), // 数据帧
  voice(3); // 语音帧

  const TK8620FrameType(this.value);
  final int value;

  static TK8620FrameType fromInt(int value) {
    return TK8620FrameType.values.firstWhere(
      (e) => e.value == value,
      orElse: () => TK8620FrameType.data,
    );
  }
}

/// TK8620通信模式，对应FrameCtrl字段的1:0位
/// 00: 数据通信, 01: PTT, 10: 实时对讲
enum TK8620CommunicationMode {
  data(0), // 数据通信
  ptt(1), // PTT
  realTime(2); // 实时对讲

  const TK8620CommunicationMode(this.value);
  final int value;

  static TK8620CommunicationMode fromInt(int value) {
    return TK8620CommunicationMode.values.firstWhere(
      (e) => e.value == value,
      orElse: () => TK8620CommunicationMode.data,
    );
  }
}

/// TK8620协议版本，对应FrameCtrl字段的5:4位
/// 当前版本: 00 (aiTalk_1.0)
class TK8620Version {
  static const int aiTalk_1_0 = 0;
}

/// TK8620协议帧控制字段相关常量
class TK8620FrameCtrl {
  // 位域掩码
  static const int versionMask = 0x30; // 位域5:4
  static const int frameTypeMask = 0x0C; // 位域3:2
  static const int commModeMask = 0x03; // 位域1:0

  // 位域移位
  static const int versionShift = 4;
  static const int frameTypeShift = 2;
  static const int commModeShift = 0;
}

/// TK8620协议帧结构
class TK8620Frame {
  TK8620Frame({
    required this.frameType,
    required this.communicationMode,
    this.version = TK8620Version.aiTalk_1_0,
    this.frameCnt = 0,
    required this.srcId,
    this.dstId = 0,
    this.subPkgNum = 1,
    this.subPkgNo = 0,
    required this.payload,
    this.useLongSrcId = false,
  });

  /// 帧类型：会话帧、命令帧、数据帧、语音帧
  final TK8620FrameType frameType;

  /// 通信模式：数据通信、PTT、实时对讲
  final TK8620CommunicationMode communicationMode;

  /// 协议版本，默认为aiTalk_1.0 (0)
  final int version;

  /// 帧序号，仅在非语音帧时使用
  final int frameCnt;

  /// 群组分配的源设备短地址
  final int srcId;

  /// 是否使用4字节的SrcID (用于加入会话请求/响应)
  final bool useLongSrcId;

  /// 群组ID，仅在非语音帧时使用
  final int dstId;

  /// 分包数量，仅在非语音帧时使用
  final int subPkgNum;

  /// 分包序号，所有帧均使用
  final int subPkgNo;

  /// 帧载荷
  final Uint8List payload;

  /// 判断当前帧是否为语音帧
  bool get isVoiceFrame => frameType == TK8620FrameType.voice;

  /// 生成帧控制字段值
  int get frameCtrl {
    return ((version & 0x03) << TK8620FrameCtrl.versionShift) |
        ((frameType.value & 0x03) << TK8620FrameCtrl.frameTypeShift) |
        ((communicationMode.value & 0x03) << TK8620FrameCtrl.commModeShift);
  }

  @override
  String toString() {
    return 'TK8620Frame(type: $frameType, mode: $communicationMode, '
        'src: $srcId, dst: $dstId, cnt: $frameCnt, '
        'pkg: $subPkgNo/$subPkgNum, payload: ${payload.length} bytes)';
  }
}

/// 会话消息控制码常量
class TK8620SessionCode {
  static const int joinRequest = 0x00; // 加入会话请求
  static const int joinResponse = 0x01; // 加入会话响应
  static const int createTalkRequest = 0x02; // 建立通话请求 (建立实时对讲通道请求)
  static const int createTalkResponse = 0x03; // 建立通话响应 (对建立实时对讲通道请求的响应)
  static const int establish = 0x04; // 建立会话通知
  static const int joinNotify = 0x05; // 加入会话通知
  static const int terminate = 0x06; // 中止会话通知
  static const int establishComplete = 0x07; // 建立会话完成通知
}

/// 数据消息类型码常量
class TK8620DataType {
  static const int text = 0x00; // 文本数据
  static const int image = 0x01; // 图像数据
  static const int voice = 0x02; // 语音数据（PTT）
  static const int gps = 0x03; // GPS数据
  static const int realTimeVoice = 0x04; // 实时通话语音数据
}

/// SessionID生成器 (参考规范: aiTalk SDD §4.1.3.5)
class TK8620SessionIdGenerator {
  static int _sessionCounter = 0;

  /// 生成SessionID
  static int generateSessionId(int deviceId) {
    // 设备ID的低16位作为前缀
    final devicePrefix = deviceId & 0xFFFF;

    // 递增会话计数器
    _sessionCounter = (_sessionCounter + 1) & 0xFFFF;

    // 组合成32位SessionID
    return (devicePrefix << 16) | _sessionCounter;
  }
}
