import 'dart:async';
import 'package:flutter/foundation.dart';
import 'dart:typed_data';
import 'sn_parser.dart';
import 'binary_escape.dart';

/// 8620 AT 指令响应类别
enum AtResponseType {
  version,
  serialNumber,
  deviceId,
  freq,
  rate,
  bcnId,
  addtl,
  listen,
  frameConfig,
  workMode,
  diData,
  ok,
  error,
  unknown,
}

/// 解析后的响应对象
class AtResponse {
  AtResponse({required this.type, required this.raw, this.payload});

  final AtResponseType type;
  final String raw;
  final Map<String, dynamic>? payload;
}

/// 负责解析并广播 8620 指令响应
class AtResponseHandler {
  AtResponseHandler._();
  static final AtResponseHandler instance = AtResponseHandler._();

  final StreamController<AtResponse> _controller =
      StreamController<AtResponse>.broadcast();

  // (保持单一职责，不在此处解析 TK8620 协议)

  Stream<AtResponse> get responses => _controller.stream;

  /// 入口：传入一行解密后的响应字符串
  void handle(String msg) {
    if (msg.trim().isEmpty) return;

    final res = _parse(msg.trim());
    _controller.add(res);

    // 调试输出 (对 diData 仅打印十六进制字符串, 隐藏 raw Uint8List)
    String display;
    if (res.type == AtResponseType.diData) {
      display = res.payload?['data'] ?? '<no data>';
      debugPrint('📥 Parsed Response: ${res.type} -> $display');
      debugPrint('[AtResponseHandler] 广播diData响应到监听器');
    } else {
      display = (res.payload ?? res.raw).toString();
      debugPrint('📥 Parsed Response: ${res.type} -> $display');
    }
  }

  AtResponse _parse(String msg) {
    if (msg == 'AT_OK') {
      return AtResponse(type: AtResponseType.ok, raw: msg);
    }
    if (msg.startsWith('AT_ERROR')) {
      return AtResponse(type: AtResponseType.error, raw: msg);
    }
    if (msg.startsWith('+VER')) {
      String? version;
      final idx = msg.indexOf(':');
      if (idx != -1 && msg.length > idx + 1) {
        // 从冒号后开始，截取到首个空白/控制字符或字符串结尾
        final rest = msg.substring(idx + 1);
        final match = RegExp(r'^[^\s\r\n]+').firstMatch(rest);
        if (match != null) {
          version = match.group(0);
        }
      }
      return AtResponse(
        type: AtResponseType.version,
        raw: msg,
        payload: version != null ? {'version': version} : null,
      );
    }
    if (msg.startsWith('+EFUSESN')) {
      return AtResponse(type: AtResponseType.serialNumber, raw: msg);
    }
    if (msg.startsWith('+SN ')) {
      final deviceId = parseDeviceId(msg);
      return AtResponse(
        type: AtResponseType.deviceId,
        raw: msg,
        payload: {'deviceId': deviceId},
      );
    }
    if (msg.startsWith('+FREQ:')) {
      final parts = msg.substring(6).split(',');
      return AtResponse(
        type: AtResponseType.freq,
        raw: msg,
        payload: {
          'txDataFreq': parts[0],
          'rxDataFreq': parts[1],
          'txBcnFreq': parts[2],
          'rxBcnFreq': parts[3],
        },
      );
    }
    if (msg.startsWith('+RATE:')) {
      return AtResponse(
        type: AtResponseType.rate,
        raw: msg,
        payload: {'rateMode': msg.substring(6)},
      );
    }
    if (msg.startsWith('+BCNID:')) {
      return AtResponse(
        type: AtResponseType.bcnId,
        raw: msg,
        payload: {'bcnId': msg.substring(7)},
      );
    }
    if (msg.startsWith('+ADDTL:')) {
      return AtResponse(
        type: AtResponseType.addtl,
        raw: msg,
        payload: {'mode': msg.substring(7)},
      );
    }
    if (msg.startsWith('+FRAMECFG:')) {
      // 解析时隙配置响应
      // 格式：+FRAMECFG:<时隙类型>,<时隙长度>,<时隙类型>,<时隙长度>,...
      final configStr = msg.substring(10);
      final parts = configStr.split(',');

      // 解析时隙配置
      final slots = <Map<String, int>>[];
      for (int i = 0; i < parts.length; i += 2) {
        if (i + 1 < parts.length) {
          final type = int.tryParse(parts[i]);
          final length = int.tryParse(parts[i + 1]);
          if (type != null && length != null) {
            slots.add({'type': type, 'length': length});
          }
        }
      }

      return AtResponse(
        type: AtResponseType.frameConfig,
        raw: msg,
        payload: {'slots': slots},
      );
    }
    if (msg.startsWith('+WORKMODE:')) {
      // 解析工作模式响应
      // 格式：+WORKMODE:<工作模式>
      final modeStr = msg.substring(10);
      final mode = int.tryParse(modeStr);

      return AtResponse(
        type: AtResponseType.workMode,
        raw: msg,
        payload: {'mode': mode},
      );
    }

    // +DI: LEN 50, SLOT 0, SNR 11, RSSI -60, Data <escaped bytes>
    if (msg.startsWith('+DI:')) {
      // 仅提取 Data 后面的十六进制字节串
      final dataIdx = msg.indexOf('Data');
      if (dataIdx != -1 && msg.length > dataIdx + 4) {
        // 注意: 不能使用 trim(), 因为 0x0C (Form Feed) 会被当做空白字符被无意删掉
        // 仅移除首尾空格, 保留其他控制字节
        String dataStr = msg.substring(dataIdx + 4);
        if (dataStr.startsWith(' ')) dataStr = dataStr.substring(1);
        if (dataStr.endsWith(' ')) {
          dataStr = dataStr.substring(0, dataStr.length - 1);
        }

        // Data 字段应始终是二进制(已转义)字节流，按原始字节处理。
        final rawEscaped = Uint8List.fromList(dataStr.codeUnits);

        // 解码并转换为十六进制字符串，便于日志查看
        final unescaped = BinaryEscape.unescape(rawEscaped);

        final dataHex = unescaped
            .map((b) => b.toRadixString(16).padLeft(2, '0'))
            .join(' ') // 以空格分隔提升可读性
            .toUpperCase();

        // 同时把未转义的原始字节放入 payload，供上层模块做 TK8620 解析
        return AtResponse(
          type: AtResponseType.diData,
          raw: msg,
          payload: {
            'data': dataHex, // 便于日志查看
            'raw': unescaped, // Uint8List，供后续协议解析
          },
        );
      }
    }
    // 未识别
    return AtResponse(type: AtResponseType.unknown, raw: msg);
  }

  /// 确保资源释放（可在应用关闭时调用）
  Future<void> dispose() async {
    await _controller.close();
  }
}
