/// 文本数据多包组装器
/// 将 TK8620 多包文本帧重组为完整字符串后返回 TK8620TextData

import 'dart:typed_data';
import 'tk8620_frame_decoder.dart';
import 'tk8620_protocol.dart';

class _TextAssembly {
  _TextAssembly(this.total)
      : chunks = List<Uint8List?>.filled(total, null, growable: false);

  final int total;
  final List<Uint8List?> chunks;

  bool get isComplete => !chunks.contains(null);

  Uint8List merge() {
    final builder = BytesBuilder();
    for (final part in chunks) {
      builder.add(part!);
    }
    return builder.toBytes();
  }
}

/// 提供 `handleTextFrame`，在收齐所有分包时返回解析后的 TK8620TextData。
class TextPacketAssembler {
  final Map<String, _TextAssembly> _cache = {};

  /// 处理一帧文本数据包，必要时进行组装
  TK8620TextData? handleTextFrame(TK8620Frame frame) {
    final total = frame.subPkgNum;
    final idx = frame.subPkgNo;

    // 单包直接返回
    if (total <= 1) {
      return TK8620PayloadParser.parseTextData(frame.payload);
    }

    // 使用 srcId+frameCnt 作为组装 key
    final key = '${frame.srcId}-${frame.frameCnt}';
    var assembly = _cache[key];
    if (assembly == null) {
      assembly = _TextAssembly(total);
      _cache[key] = assembly;
    }

    assembly.chunks[idx] = frame.payload;

    if (assembly.isComplete) {
      final merged = assembly.merge();
      _cache.remove(key);
      return TK8620PayloadParser.parseTextData(merged);
    }

    return null; // 未组装完成
  }
} 