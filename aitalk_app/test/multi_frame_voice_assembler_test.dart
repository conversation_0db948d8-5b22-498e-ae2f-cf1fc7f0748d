import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo_ios/core/audio/multi_frame_voice_assembler.dart';

void main() {
  group('MultiFrameVoiceAssembler Tests', () {
    late MultiFrameVoiceAssembler assembler;

    setUp(() {
      assembler = MultiFrameVoiceAssembler.instance;
    });

    test('should pack and unpack single frame correctly', () {
      // 创建一个11字节的MELP帧
      final frame = Uint8List.fromList(List.generate(11, (i) => i + 1));
      final frames = [frame];

      // 打包
      final packed = assembler.packFrames(frames);

      // 验证打包结果：1字节帧数量 + 11字节数据
      expect(packed.length, equals(12));
      expect(packed[0], equals(1)); // 帧数量

      // 解包
      final unpacked = assembler.unpackFrames(packed);

      // 验证解包结果
      expect(unpacked.length, equals(1));
      expect(unpacked[0], equals(frame));
    });

    test('should pack and unpack multiple frames correctly', () {
      // 创建5个11字节的MELP帧
      final frames = List.generate(
        5,
        (i) => Uint8List.fromList(List.generate(11, (j) => i * 11 + j + 1)),
      );

      // 打包
      final packed = assembler.packFrames(frames);

      // 验证打包结果：1字节帧数量 + 5*11字节数据
      expect(packed.length, equals(56));
      expect(packed[0], equals(5)); // 帧数量

      // 解包
      final unpacked = assembler.unpackFrames(packed);

      // 验证解包结果
      expect(unpacked.length, equals(5));
      for (int i = 0; i < 5; i++) {
        expect(unpacked[i], equals(frames[i]));
      }
    });

    test('should handle empty frame list', () {
      final frames = <Uint8List>[];
      final packed = assembler.packFrames(frames);

      expect(packed.length, equals(0));

      final unpacked = assembler.unpackFrames(packed);
      expect(unpacked.length, equals(0));
    });

    test('should handle invalid frame size', () {
      // 创建一个错误大小的帧（不是11字节）
      final invalidFrame = Uint8List.fromList([1, 2, 3, 4, 5]); // 5字节
      final frames = [invalidFrame];

      // 应该抛出异常
      expect(() => assembler.packFrames(frames), throwsArgumentError);
    });

    test('should handle corrupted packed data', () {
      // 创建一个损坏的数据包
      final corruptedData = Uint8List.fromList([3, 1, 2, 3]); // 声称有3帧，但数据不足

      final unpacked = assembler.unpackFrames(corruptedData);
      expect(unpacked.length, equals(0)); // 应该返回空列表
    });

    test('should merge PCM frames correctly', () {
      // 创建3个不同长度的PCM帧
      final pcmFrames = [
        Int16List.fromList([1, 2, 3]),
        Int16List.fromList([4, 5]),
        Int16List.fromList([6, 7, 8, 9]),
      ];

      final merged = assembler.mergePcmFrames(pcmFrames);

      expect(merged.length, equals(9));
      expect(merged, equals(Int16List.fromList([1, 2, 3, 4, 5, 6, 7, 8, 9])));
    });

    test('should handle empty PCM frames list', () {
      final pcmFrames = <Int16List>[];
      final merged = assembler.mergePcmFrames(pcmFrames);

      expect(merged.length, equals(0));
    });

    test('should validate constants', () {
      expect(MultiFrameVoiceAssembler.framesPerPacket, equals(5));
      expect(MultiFrameVoiceAssembler.melpFrameSize, equals(11));
      expect(MultiFrameVoiceAssembler.packetSize, equals(55));
    });

    test('should pack exactly 5 frames as expected', () {
      // 创建正好5帧的数据
      final frames = List.generate(
        5,
        (i) => Uint8List.fromList(List.generate(11, (j) => (i + 1) * 10 + j)),
      );

      final packed = assembler.packFrames(frames);

      // 验证：1字节帧数量 + 5*11字节数据 = 56字节
      expect(packed.length, equals(56));
      expect(packed[0], equals(5));

      // 验证每帧数据都正确打包
      for (int i = 0; i < 5; i++) {
        final frameStart = 1 + i * 11;
        final frameEnd = frameStart + 11;
        final extractedFrame = packed.sublist(frameStart, frameEnd);
        expect(extractedFrame, equals(frames[i]));
      }
    });
  });
}
