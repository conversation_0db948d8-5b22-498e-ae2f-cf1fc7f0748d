import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo_ios/core/audio/melp_codec.dart';
import 'dart:typed_data';
import 'dart:math';

void main() {
  group('MELP Codec Tests', () {
    late MelpAudioCodec codec2400;
    late MelpAudioCodec codec1200;

    setUpAll(() async {
      // 初始化2.4kbps编解码器
      codec2400 = MelpCodecFactory.create2400();
      await codec2400.init();

      // 初始化1.2kbps编解码器  
      codec1200 = MelpCodecFactory.create1200();
      await codec1200.init();
    });

    tearDownAll(() async {
      await codec2400.dispose();
      await codec1200.dispose();
    });

    test('MELP 2.4kbps codec initialization', () {
      expect(codec2400.isInitialized, isTrue);
      expect(codec2400.rate, equals(2400));
      expect(codec2400.frameSize, equals(180));
      expect(codec2400.encodedBytesPerFrame, equals(7));
    });

    test('MELP 1.2kbps codec initialization', () {
      expect(codec1200.isInitialized, isTrue);
      expect(codec1200.rate, equals(1200));
      expect(codec1200.frameSize, equals(540));
      expect(codec1200.encodedBytesPerFrame, equals(11));
    });

    test('MELP 2.4kbps encode/decode test', () {
      // 生成测试PCM数据（180个样本）
      final pcmData = _generateTestPcm(180);
      
      // 编码
      final encoded = codec2400.encode(pcmData);
      expect(encoded.length, equals(7));

      // 解码
      final decoded = codec2400.decode(encoded);
      expect(decoded.length, equals(180));

      // 验证解码数据不为空
      expect(decoded.any((sample) => sample != 0), isTrue);
    });

    test('MELP 1.2kbps encode/decode test', () {
      // 生成测试PCM数据（540个样本）
      final pcmData = _generateTestPcm(540);
      
      // 编码
      final encoded = codec1200.encode(pcmData);
      expect(encoded.length, equals(11));

      // 解码
      final decoded = codec1200.decode(encoded);
      expect(decoded.length, equals(540));

      // 验证解码数据不为空
      expect(decoded.any((sample) => sample != 0), isTrue);
    });

    test('MELP 2.4kbps process test', () {
      final pcmData = _generateTestPcm(180);
      final processed = codec2400.processTest(pcmData);
      
      expect(processed.length, equals(180));
      expect(processed.any((sample) => sample != 0), isTrue);
    });

    test('MELP 1.2kbps process test', () {
      final pcmData = _generateTestPcm(540);
      final processed = codec1200.processTest(pcmData);
      
      expect(processed.length, equals(540));
      expect(processed.any((sample) => sample != 0), isTrue);
    });

    test('MELP rate switching test', () {
      // 测试从2400切换到1200
      codec2400.setRate(1200);
      expect(codec2400.frameSize, equals(540));
      expect(codec2400.encodedBytesPerFrame, equals(11));

      // 测试1200bps编解码
      final pcmData = _generateTestPcm(540);
      final encoded = codec2400.encode(pcmData);
      expect(encoded.length, equals(11));

      final decoded = codec2400.decode(encoded);
      expect(decoded.length, equals(540));

      // 切换回2400
      codec2400.setRate(2400);
      expect(codec2400.frameSize, equals(180));
      expect(codec2400.encodedBytesPerFrame, equals(7));
    });

    test('MELP error handling - wrong PCM size', () {
      // 测试错误的PCM大小
      final wrongSizePcm = _generateTestPcm(100); // 应该是180
      
      expect(() => codec2400.encode(wrongSizePcm), 
             throwsA(isA<ArgumentError>()));
    });

    test('MELP error handling - wrong encoded data size', () {
      // 测试错误的编码数据大小
      final wrongSizeData = Uint8List(5); // 应该是7字节
      
      expect(() => codec2400.decode(wrongSizeData), 
             throwsA(isA<ArgumentError>()));
    });

    test('MELP error handling - uninitialized codec', () {
      final uninitializedCodec = MelpAudioCodec(rate: 2400);
      final pcmData = _generateTestPcm(180);
      
      expect(() => uninitializedCodec.encode(pcmData), 
             throwsA(isA<StateError>()));
      expect(() => uninitializedCodec.decode(Uint8List(7)), 
             throwsA(isA<StateError>()));
    });

    test('MELP factory methods', () {
      final codec2400Factory = MelpCodecFactory.create2400();
      final codec1200Factory = MelpCodecFactory.create1200();
      final codecWithRate = MelpCodecFactory.createWithRate(2400);

      expect(codec2400Factory.rate, equals(2400));
      expect(codec1200Factory.rate, equals(1200));
      expect(codecWithRate.rate, equals(2400));
    });

    test('MELP invalid rate handling', () {
      expect(() => MelpAudioCodec(rate: 3000), 
             throwsA(isA<ArgumentError>()));
      expect(() => MelpCodecFactory.createWithRate(3000), 
             throwsA(isA<ArgumentError>()));
    });
  });
}

/// 生成测试用的PCM数据
/// [length] 样本数量
Int16List _generateTestPcm(int length) {
  final random = Random(42); // 使用固定种子确保测试可重复
  final pcm = Int16List(length);
  
  for (int i = 0; i < length; i++) {
    // 生成-32768到32767范围内的随机16位整数
    pcm[i] = (random.nextDouble() * 65536 - 32768).round();
  }
  
  return pcm;
}

/// 生成正弦波测试数据
/// [length] 样本数量
/// [frequency] 频率（Hz）
/// [sampleRate] 采样率（Hz）
Int16List _generateSineWave(int length, double frequency, int sampleRate) {
  final pcm = Int16List(length);
  const amplitude = 16384; // 约为最大值的一半
  
  for (int i = 0; i < length; i++) {
    final t = i / sampleRate;
    final sample = amplitude * sin(2 * pi * frequency * t);
    pcm[i] = sample.round();
  }
  
  return pcm;
}
